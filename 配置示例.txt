# PowerPoint 小牛翻译器 配置示例

## 1. API配置（必须修改）

在 PPT翻译器.bas 文件中找到以下行并修改：

```vba
Private Const APP_ID As String = "您的appId"      ' 替换为您从小牛翻译获取的appId
Private Const API_KEY As String = "您的apikey"   ' 替换为您从小牛翻译获取的apikey
```

## 2. 文件路径配置（可选修改）

在 TranslatePPT() 函数中找到以下行并根据需要修改：

```vba
sourcePath = "D:\test\待翻译PPT.pptm"    ' 源PPT文件路径
targetPath = "D:\test\translated.pptx"   ' 翻译后文件保存路径
```

## 3. 语言配置（可选修改）

如需修改翻译语言对，在文件顶部找到：

```vba
Private Const FROM_LANG As String = "zh"  ' 源语言：zh=中文
Private Const TO_LANG As String = "en"    ' 目标语言：en=英文
```

支持的语言代码：
- zh: 中文
- en: 英文
- ja: 日语
- ko: 韩语
- fr: 法语
- de: 德语
- es: 西班牙语
- ru: 俄语

## 4. 延时配置（可选修改）

如需调整API调用频率，修改：

```vba
Private Const REQUEST_DELAY As Integer = 200  ' API调用间隔（毫秒）
```

在 TranslatePPT() 函数中的幻灯片间延时：

```vba
Application.Wait (Now + TimeValue("0:00:01"))  ' 幻灯片间延时1秒
```

## 5. 小牛翻译API获取步骤

1. 访问：https://niutrans.com/
2. 注册账号并登录
3. 进入"控制台" -> "API应用"
4. 创建新应用或查看现有应用
5. 复制 appId 和 apikey

## 6. 快速测试配置

修改配置后，可以创建一个简单的测试PPT：
1. 创建新的PowerPoint文件
2. 添加一个文本框，输入"你好世界"
3. 保存为指定路径的.pptm文件
4. 运行VBA代码测试

## 注意事项

- API密钥请妥善保管，不要泄露给他人
- 小牛翻译有免费额度限制，超出后需要付费
- 建议先用小文件测试配置是否正确
- 确保网络连接稳定
