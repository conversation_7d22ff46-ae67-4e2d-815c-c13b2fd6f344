Option Explicit

' Windows API声明用于延时
#If VBA7 Then
    Private Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#Else
    Private Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If

' 小牛翻译API配置
Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"
Private Const API_URL As String = "http://api.niutrans.com/NiuTransServer/translation"

' 快速测试主函数
Sub QuickTranslationTest()
    Debug.Print "=== 快速翻译测试 ==="
    Debug.Print "时间: " & Now()
    Debug.Print ""
    
    ' 测试单个文本翻译
    Dim testText As String
    Dim result As String
    
    testText = "你好，世界！"
    Debug.Print "测试文本: " & testText
    
    result = TranslateTextQuick(testText, "zh", "en")
    
    If Len(result) > 0 And result <> testText Then
        Debug.Print "翻译结果: " & result
        Debug.Print "状态: ✓ 成功"
        MsgBox "翻译测试成功！" & vbCrLf & vbCrLf & "原文: " & testText & vbCrLf & "译文: " & result, vbInformation
    Else
        Debug.Print "翻译结果: [失败]"
        Debug.Print "状态: ✗ 失败"
        MsgBox "翻译测试失败，请检查网络连接和API配置。", vbCritical
    End If
    
    Debug.Print "=== 测试结束 ==="
End Sub

' 简化的翻译函数
Function TranslateTextQuick(sourceText As String, fromLang As String, toLang As String) As String
    On Error GoTo ErrorHandler
    
    ' 清理文本
    sourceText = Trim(sourceText)
    If Len(sourceText) = 0 Then
        TranslateTextQuick = ""
        Exit Function
    End If
    
    Debug.Print "发送翻译请求..."
    
    ' 创建HTTP请求对象
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 构建POST数据
    Dim postData As String
    postData = "from=" & fromLang & "&to=" & toLang & "&apikey=" & API_KEY & "&src_text=" & sourceText
    
    ' 发送POST请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded; charset=UTF-8"
    http.setRequestHeader "Accept", "application/json"
    
    ' 发送请求
    http.send postData
    
    Debug.Print "HTTP状态: " & http.Status
    
    ' 检查响应状态
    If http.Status = 200 Then
        Dim responseText As String
        responseText = http.responseText
        
        Debug.Print "API响应: " & responseText
        
        ' 解析JSON响应
        Dim translatedText As String
        translatedText = ParseJSONQuick(responseText)
        
        If Len(translatedText) > 0 Then
            TranslateTextQuick = translatedText
        Else
            Debug.Print "解析失败"
            TranslateTextQuick = ""
        End If
    Else
        Debug.Print "HTTP请求失败: " & http.Status
        TranslateTextQuick = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "翻译出错: " & Err.Description
    TranslateTextQuick = ""
End Function

' 简化的JSON解析
Function ParseJSONQuick(jsonResponse As String) As String
    On Error GoTo ErrorHandler
    
    ' 查找"tgt_text":"
    Dim startPos As Integer
    Dim endPos As Integer
    Dim targetText As String
    
    startPos = InStr(jsonResponse, """tgt_text"":""")
    If startPos > 0 Then
        startPos = startPos + 12 ' 跳过"tgt_text":"
        endPos = InStr(startPos, jsonResponse, """")
        If endPos > startPos Then
            targetText = Mid(jsonResponse, startPos, endPos - startPos)
            
            ' 处理基本转义字符
            targetText = Replace(targetText, "\n", vbCrLf)
            targetText = Replace(targetText, "\""", """")
            
            ParseJSONQuick = Trim(targetText)
        End If
    End If
    
    Exit Function
    
ErrorHandler:
    ParseJSONQuick = ""
End Function

' 测试文件路径
Sub TestFilePaths()
    Debug.Print "=== 文件路径测试 ==="
    
    Dim sourcePath As String
    Dim targetPath As String
    
    sourcePath = "D:\Test\PPT翻译_提示语\待翻译\待翻译PPT.pptm"
    targetPath = "D:\Test\PPT翻译_提示语\待翻译\translated.pptx"
    
    Debug.Print "源文件路径: " & sourcePath
    Debug.Print "目标文件路径: " & targetPath
    
    ' 检查源文件是否存在
    If Dir(sourcePath) <> "" Then
        Debug.Print "源文件状态: ✓ 存在"
    Else
        Debug.Print "源文件状态: ✗ 不存在"
    End If
    
    ' 检查目标目录是否存在
    Dim targetDir As String
    targetDir = Left(targetPath, InStrRev(targetPath, "\"))
    
    If Dir(targetDir, vbDirectory) <> "" Then
        Debug.Print "目标目录状态: ✓ 存在"
    Else
        Debug.Print "目标目录状态: ✗ 不存在"
    End If
    
    Debug.Print "=== 路径测试结束 ==="
End Sub

' 测试PowerPoint对象
Sub TestPowerPointObjects()
    Debug.Print "=== PowerPoint对象测试 ==="
    
    On Error GoTo ErrorHandler
    
    ' 测试PowerPoint应用程序
    Dim pptApp As Object
    Set pptApp = Application
    
    Debug.Print "PowerPoint版本: " & pptApp.Version
    Debug.Print "PowerPoint名称: " & pptApp.Name
    
    ' 测试演示文稿集合
    Debug.Print "当前打开的演示文稿数量: " & pptApp.Presentations.Count
    
    Debug.Print "PowerPoint对象测试: ✓ 成功"
    Debug.Print "=== 对象测试结束 ==="
    Exit Sub
    
ErrorHandler:
    Debug.Print "PowerPoint对象测试: ✗ 失败 - " & Err.Description
    Debug.Print "=== 对象测试结束 ==="
End Sub

' 综合测试
Sub RunAllTests()
    Debug.Print "========================================"
    Debug.Print "开始运行所有测试"
    Debug.Print "========================================"
    
    ' 测试1: 文件路径
    TestFilePaths
    Debug.Print ""
    
    ' 测试2: PowerPoint对象
    TestPowerPointObjects
    Debug.Print ""
    
    ' 测试3: 翻译功能
    QuickTranslationTest
    Debug.Print ""
    
    Debug.Print "========================================"
    Debug.Print "所有测试完成"
    Debug.Print "========================================"
    
    MsgBox "所有测试完成！请查看立即窗口了解详细结果。", vbInformation
End Sub
