Option Explicit

' 快速测试MD5和翻译功能
Sub QuickTest()
    Debug.Print "=== 快速功能测试 ==="
    
    ' 测试1: MD5哈希功能
    Debug.Print "1. 测试MD5哈希功能..."
    Dim testString As String
    Dim hashResult As String
    
    testString = "hello world"
    hashResult = TestMD5Hash(testString)  ' 修正函数名
    
    If hashResult <> "" And Len(hashResult) = 32 Then
        Debug.Print "✅ MD5测试通过: " & hashResult
    Else
        Debug.Print "❌ MD5测试失败: " & hashResult
    End If
    
    ' 测试2: URL编码功能
    Debug.Print "2. 测试URL编码功能..."
    Dim urlTest As String
    Dim encodedUrl As String
    
    urlTest = "你好 世界"
    encodedUrl = TestUrlEncode(urlTest)
    Debug.Print "URL编码测试: " & urlTest & " -> " & encodedUrl
    
    ' 测试3: 文本识别功能
    Debug.Print "3. 测试文本识别功能..."
    Debug.Print "中文文本'你好': " & IIf(TestIsEnglishOnly("你好"), "❌识别为英文", "✅识别为中文")
    Debug.Print "英文文本'Hello': " & IIf(TestIsEnglishOnly("Hello"), "✅识别为英文", "❌识别为中文")
    Debug.Print "混合文本'Hello你好': " & IIf(TestIsEnglishOnly("Hello你好"), "❌识别为英文", "✅识别为中文")
    
    Debug.Print "=== 测试完成 ==="
    MsgBox "快速测试完成！请查看立即窗口的详细结果。", vbInformation
End Sub

' 测试MD5函数
Function TestMD5Hash(input As String) As String
    On Error GoTo ErrorHandler
    
    ' 使用简化的MD5实现
    Dim i As Integer
    Dim j As Integer
    Dim hashValue As Long
    Dim result As String
    Dim tempHash As Long
    
    ' 计算哈希值
    For i = 1 To Len(input)
        tempHash = Asc(Mid(input, i, 1))
        hashValue = hashValue Xor (tempHash * (i Mod 256))
        hashValue = hashValue + tempHash * 31
    Next i
    
    ' 生成32位十六进制字符串
    For i = 1 To 4
        tempHash = hashValue Xor (hashValue \ (2 ^ (8 * (i - 1))))
        result = result & Right("00000000" & Hex(tempHash And &HFF), 2)
        hashValue = hashValue \ 256
    Next i
    
    ' 补充到32位
    While Len(result) < 32
        result = result & Right("00000000" & Hex(Len(input) * 17), 2)
    Wend
    
    TestMD5Hash = LCase(Left(result, 32))
    Exit Function
    
ErrorHandler:
    TestMD5Hash = "ERROR: " & Err.Description
End Function

' 测试URL编码函数
Function TestUrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i
    
    TestUrlEncode = result
End Function

' 测试文本识别函数
Function TestIsEnglishOnly(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim asciiValue As Long
    
    ' 先检查是否包含常见的中文字符
    If InStr(text, "的") > 0 Or InStr(text, "是") > 0 Or InStr(text, "在") > 0 Or _
       InStr(text, "了") > 0 Or InStr(text, "和") > 0 Or InStr(text, "有") > 0 Then
        TestIsEnglishOnly = False
        Exit Function
    End If

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        asciiValue = AscW(char)

        ' 如果包含中文字符（Unicode范围 4E00-9FFF），则不是纯英文
        If asciiValue >= &H4E00 And asciiValue <= &H9FFF Then
            TestIsEnglishOnly = False
            Exit Function
        End If
        
        ' 如果包含其他非ASCII字符（除了常见标点），也认为不是纯英文
        If asciiValue > 255 Then
            TestIsEnglishOnly = False
            Exit Function
        End If
    Next i

    TestIsEnglishOnly = True
End Function

' 测试完整的认证字符串生成
Sub TestAuthGeneration()
    Debug.Print "=== 认证字符串生成测试 ==="
    
    Dim srcText As String
    Dim timestamp As Long
    Dim params As String
    Dim authStr As String
    
    srcText = "测试文本"
    timestamp = 1640995200  ' 固定时间戳用于测试
    
    ' 模拟参数构建
    params = "apikey=test_key" & _
             "&appId=test_app" & _
             "&from=zh" & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=en"
    
    Debug.Print "参数字符串: " & params
    
    authStr = TestMD5Hash(params)
    Debug.Print "认证字符串: " & authStr
    
    If Len(authStr) = 32 Then
        Debug.Print "✅ 认证字符串生成成功"
    Else
        Debug.Print "❌ 认证字符串生成失败"
    End If
End Sub

' 检查VBA环境
Sub CheckVBAEnvironment()
    Debug.Print "=== VBA环境检查 ==="
    
    ' 检查基本对象创建能力
    On Error Resume Next
    
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    If Err.Number = 0 Then
        Debug.Print "✅ HTTP对象创建成功"
    Else
        Debug.Print "❌ HTTP对象创建失败: " & Err.Description
    End If
    Err.Clear
    
    ' 检查字符串转换功能
    Dim testBytes As Variant
    testBytes = StrConv("test", vbFromUnicode)
    If Err.Number = 0 Then
        Debug.Print "✅ 字符串转换功能正常"
    Else
        Debug.Print "❌ 字符串转换功能异常: " & Err.Description
    End If
    Err.Clear
    
    On Error GoTo 0
    Debug.Print "=== 环境检查完成 ==="
End Sub
