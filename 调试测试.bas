Option Explicit

' Windows API声明用于延时
#If VBA7 Then
    Private Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#Else
    Private Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If

' 测试IsEnglishOnly函数
Sub TestIsEnglishOnly()
    Debug.Print "=== IsEnglishOnly函数测试 ==="
    Debug.Print "时间: " & Now()
    Debug.Print ""
    
    ' 测试用例
    Dim testCases As Variant
    testCases = Array( _
        "Hello World", _
        "你好世界", _
        "Hello 你好", _
        "PowerPoint演示文稿", _
        "数据分析报告", _
        "123", _
        "ABC123", _
        "测试，文本。", _
        "Test！", _
        "" _
    )
    
    Dim i As Integer
    Dim testText As String
    Dim result As Boolean
    
    For i = 0 To UBound(testCases)
        testText = testCases(i)
        result = IsEnglishOnlyTest(testText)
        
        Debug.Print "文本: """ & testText & """"
        Debug.Print "IsEnglishOnly结果: " & result
        Debug.Print "预期: " & GetExpectedResult(testText)
        Debug.Print "---"
    Next i
    
    Debug.Print "=== 测试结束 ==="
End Sub

' 测试版本的IsEnglishOnly函数（与主程序相同）
Function IsEnglishOnlyTest(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim asciiValue As Integer
    Dim hasChineseChar As Boolean
    
    hasChineseChar = False
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        asciiValue = Asc(char)
        
        ' 检查是否包含中文字符
        ' 方法1: 检查ASCII值范围
        If asciiValue < 0 Or asciiValue > 255 Then
            hasChineseChar = True
            Exit For
        End If
        
        ' 方法2: 检查常见中文标点符号
        If char = "，" Or char = "。" Or char = "！" Or char = "？" Or char = "；" Or char = "：" Then
            hasChineseChar = True
            Exit For
        End If
        
        ' 方法3: 使用AscW函数检查Unicode值（如果可用）
        On Error Resume Next
        Dim unicodeValue As Integer
        unicodeValue = AscW(char)
        If Err.Number = 0 Then
            ' 中文字符的Unicode范围大致在4352-40959
            If unicodeValue >= 4352 And unicodeValue <= 40959 Then
                hasChineseChar = True
                Exit For
            End If
        End If
        On Error GoTo 0
    Next i
    
    ' 如果包含中文字符，则不是纯英文
    IsEnglishOnlyTest = Not hasChineseChar
End Function

' 获取预期结果（用于验证）
Function GetExpectedResult(text As String) As String
    Select Case text
        Case "Hello World", "123", "ABC123", ""
            GetExpectedResult = "True (纯英文)"
        Case "你好世界", "Hello 你好", "PowerPoint演示文稿", "数据分析报告", "测试，文本。", "Test！"
            GetExpectedResult = "False (包含中文)"
        Case Else
            GetExpectedResult = "未知"
    End Select
End Function

' 测试PPT文件中的实际文本
Sub TestPPTTextExtraction()
    Debug.Print "=== PPT文本提取测试 ==="
    
    Dim sourcePath As String
    sourcePath = "D:\Test\PPT翻译_提示语\待翻译\待翻译PPT.pptm"
    
    ' 检查文件是否存在
    If Dir(sourcePath) = "" Then
        Debug.Print "错误: 源文件不存在 - " & sourcePath
        Exit Sub
    End If
    
    Debug.Print "打开文件: " & sourcePath
    
    On Error GoTo ErrorHandler
    
    ' 打开PPT文件
    Dim ppt As Presentation
    Set ppt = Presentations.Open(sourcePath)
    
    Debug.Print "文件打开成功，幻灯片数量: " & ppt.Slides.Count
    
    ' 检查前几张幻灯片的文本
    Dim slideIndex As Integer
    Dim maxSlides As Integer
    maxSlides = IIf(ppt.Slides.Count > 3, 3, ppt.Slides.Count) ' 最多检查3张幻灯片
    
    For slideIndex = 1 To maxSlides
        Debug.Print ""
        Debug.Print "=== 幻灯片 " & slideIndex & " ==="
        
        Dim slide As slide
        Set slide = ppt.Slides(slideIndex)
        
        Dim shapeIndex As Integer
        For shapeIndex = 1 To slide.Shapes.Count
            Dim shape As shape
            Set shape = slide.Shapes(shapeIndex)
            
            If shape.HasTextFrame Then
                If shape.TextFrame.HasText Then
                    Dim textContent As String
                    textContent = Trim(shape.TextFrame.TextRange.Text)
                    
                    If Len(textContent) > 0 Then
                        Debug.Print "形状 " & shapeIndex & " 文本: """ & Left(textContent, 100) & """"
                        Debug.Print "IsEnglishOnly: " & IsEnglishOnlyTest(textContent)
                        Debug.Print "---"
                    End If
                End If
            End If
        Next shapeIndex
    Next slideIndex
    
    ' 关闭文件
    ppt.Close
    Debug.Print ""
    Debug.Print "=== 文本提取测试完成 ==="
    Exit Sub
    
ErrorHandler:
    Debug.Print "错误: " & Err.Description
    If Not ppt Is Nothing Then
        ppt.Close
    End If
End Sub

' 运行所有调试测试
Sub RunAllDebugTests()
    Debug.Print "========================================"
    Debug.Print "开始运行所有调试测试"
    Debug.Print "========================================"
    
    ' 测试1: IsEnglishOnly函数
    TestIsEnglishOnly
    Debug.Print ""
    
    ' 测试2: PPT文本提取
    TestPPTTextExtraction
    Debug.Print ""
    
    Debug.Print "========================================"
    Debug.Print "所有调试测试完成"
    Debug.Print "========================================"
    
    MsgBox "调试测试完成！请查看立即窗口了解详细结果。", vbInformation
End Sub
