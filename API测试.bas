Option Explicit

' Windows API声明用于延时
#If VBA7 Then
    Private Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#Else
    Private Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If

' 小牛翻译API测试模块
' 用于测试API连接和翻译功能

Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"
Private Const API_URL As String = "http://api.niutrans.com/NiuTransServer/translation"

' 测试API连接和翻译功能
Sub TestNiuTransAPI()
    Debug.Print "=== 小牛翻译API测试开始 ==="
    Debug.Print "时间: " & Now()
    Debug.Print "API密钥: " & Left(API_KEY, 8) & "..."
    Debug.Print ""
    
    ' 测试用例
    Dim testCases As Variant
    testCases = Array( _
        "你好，世界！", _
        "这是一个测试文本。", _
        "PowerPoint演示文稿", _
        "数据分析报告", _
        "项目管理计划" _
    )
    
    Dim i As Integer
    Dim originalText As String
    Dim translatedText As String
    Dim success As Integer
    Dim total As Integer
    
    total = UBound(testCases) + 1
    success = 0
    
    ' 逐个测试翻译
    For i = 0 To UBound(testCases)
        originalText = testCases(i)
        Debug.Print "测试 " & (i + 1) & "/" & total & ":"
        Debug.Print "原文: " & originalText
        
        translatedText = TranslateTextTest(originalText, "zh", "en")
        
        If Len(translatedText) > 0 And translatedText <> originalText Then
            Debug.Print "译文: " & translatedText
            Debug.Print "状态: ✓ 成功"
            success = success + 1
        Else
            Debug.Print "译文: [翻译失败]"
            Debug.Print "状态: ✗ 失败"
        End If
        
        Debug.Print ""
        
        ' 添加延时避免API限制
        Sleep 300
    Next i
    
    ' 输出测试结果
    Debug.Print "=== 测试结果汇总 ==="
    Debug.Print "总测试数: " & total
    Debug.Print "成功数: " & success
    Debug.Print "失败数: " & (total - success)
    Debug.Print "成功率: " & Format(success / total * 100, "0.0") & "%"
    
    If success = total Then
        Debug.Print "状态: 🎉 所有测试通过！API工作正常。"
        MsgBox "API测试完成！所有测试通过，翻译功能正常。", vbInformation, "测试成功"
    ElseIf success > 0 Then
        Debug.Print "状态: ⚠️ 部分测试通过，请检查网络或API配置。"
        MsgBox "API测试完成！部分测试通过，请检查调试窗口了解详情。", vbExclamation, "部分成功"
    Else
        Debug.Print "状态: ❌ 所有测试失败，请检查API密钥和网络连接。"
        MsgBox "API测试失败！请检查API密钥和网络连接。", vbCritical, "测试失败"
    End If
    
    Debug.Print "=== 测试结束 ==="
End Sub

' 测试版本的翻译函数（包含更详细的调试信息）
Function TranslateTextTest(sourceText As String, fromLang As String, toLang As String) As String
    On Error GoTo ErrorHandler
    
    Dim startTime As Double
    startTime = Timer
    
    ' 清理文本
    sourceText = Trim(sourceText)
    If Len(sourceText) = 0 Then
        TranslateTextTest = ""
        Exit Function
    End If
    
    Debug.Print "  发送API请求..."
    
    ' 创建HTTP请求对象
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 构建请求URL和参数
    Dim url As String
    Dim params As String
    
    url = API_URL
    params = "from=" & fromLang & "&to=" & toLang & "&apikey=" & API_KEY & "&src_text=" & UrlEncodeTest(sourceText)
    
    Debug.Print "  请求URL: " & url & "?" & Left(params, 100) & "..."
    
    ' 发送GET请求
    http.Open "GET", url & "?" & params, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send
    
    Dim responseTime As Double
    responseTime = Timer - startTime
    
    Debug.Print "  响应时间: " & Format(responseTime, "0.000") & "秒"
    Debug.Print "  HTTP状态: " & http.Status
    
    ' 检查响应状态
    If http.Status = 200 Then
        Dim responseText As String
        responseText = http.responseText
        
        Debug.Print "  响应内容: " & Left(responseText, 200) & "..."
        
        ' 解析JSON响应
        Dim translatedText As String
        translatedText = ParseTranslationResponseTest(responseText)
        
        If Len(translatedText) > 0 Then
            TranslateTextTest = translatedText
        Else
            Debug.Print "  错误: 无法解析翻译结果"
            TranslateTextTest = ""
        End If
    Else
        Debug.Print "  错误: HTTP请求失败，状态码 " & http.Status
        If Len(http.responseText) > 0 Then
            Debug.Print "  错误详情: " & http.responseText
        End If
        TranslateTextTest = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "  异常: " & Err.Description & " (错误号: " & Err.Number & ")"
    TranslateTextTest = ""
End Function

' 测试版本的JSON解析函数
Function ParseTranslationResponseTest(jsonResponse As String) As String
    On Error GoTo ErrorHandler
    
    Debug.Print "  解析JSON响应..."
    
    ' 检查是否包含错误信息
    If InStr(jsonResponse, """error""") > 0 Then
        Debug.Print "  API返回错误: " & jsonResponse
        ParseTranslationResponseTest = ""
        Exit Function
    End If
    
    ' 查找"tgt_text":"
    Dim startPos As Integer
    Dim endPos As Integer
    Dim targetText As String
    
    startPos = InStr(jsonResponse, """tgt_text"":""")
    If startPos > 0 Then
        startPos = startPos + 12 ' 跳过"tgt_text":"
        endPos = InStr(startPos, jsonResponse, """")
        If endPos > startPos Then
            targetText = Mid(jsonResponse, startPos, endPos - startPos)
            
            ' 处理JSON转义字符
            targetText = Replace(targetText, "\n", vbCrLf)
            targetText = Replace(targetText, "\r", "")
            targetText = Replace(targetText, "\t", vbTab)
            targetText = Replace(targetText, "\\", "\")
            targetText = Replace(targetText, "\""", """")
            
            Debug.Print "  解析成功，提取到翻译文本"
            ParseTranslationResponseTest = targetText
        Else
            Debug.Print "  解析失败：找不到结束引号"
            ParseTranslationResponseTest = ""
        End If
    Else
        Debug.Print "  解析失败：找不到tgt_text字段"
        ParseTranslationResponseTest = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "  JSON解析异常: " & Err.Description
    ParseTranslationResponseTest = ""
End Function

' 测试版本的URL编码函数（修复中文编码问题）
Function UrlEncodeTest(text As String) As String
    On Error GoTo ErrorHandler

    ' 使用.NET Framework的HttpUtility进行正确的UTF-8编码
    Dim objHTTP As Object
    Set objHTTP = CreateObject("MSXML2.XMLHTTP")

    ' 简化的URL编码，专门处理中文字符
    Dim result As String
    Dim i As Integer
    Dim char As String
    Dim charCode As Integer

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        charCode = Asc(char)

        Select Case char
            Case "A" To "Z", "a" To "z", "0" To "9", "-", "_", ".", "~"
                result = result & char
            Case " "
                result = result & "%20"
            Case Else
                ' 对于中文和其他特殊字符，使用更简单的方法
                If charCode > 127 Then
                    ' 中文字符直接传递，让API服务器处理
                    result = result & char
                Else
                    ' ASCII特殊字符进行百分号编码
                    result = result & "%" & Right("0" & Hex(charCode), 2)
                End If
        End Select
    Next i

    UrlEncodeTest = result
    Exit Function

ErrorHandler:
    Debug.Print "URL编码错误: " & Err.Description
    UrlEncodeTest = text ' 编码失败时返回原文
End Function

' 快速测试单个翻译
Sub QuickTest()
    Dim testText As String
    Dim result As String
    
    testText = InputBox("请输入要翻译的中文文本:", "快速测试", "你好，世界！")
    
    If Len(testText) > 0 Then
        Debug.Print "=== 快速翻译测试 ==="
        Debug.Print "原文: " & testText
        
        result = TranslateTextTest(testText, "zh", "en")
        
        If Len(result) > 0 Then
            Debug.Print "译文: " & result
            MsgBox "翻译成功！" & vbCrLf & vbCrLf & "原文: " & testText & vbCrLf & "译文: " & result, vbInformation
        Else
            Debug.Print "翻译失败"
            MsgBox "翻译失败，请检查网络连接和API配置。", vbCritical
        End If
    End If
End Sub


