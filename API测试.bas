Option Explicit

' 声明Windows API Sleep函数
Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)

' 小牛翻译API测试脚本
' 用于验证API配置是否正确

' API配置（请修改为您的实际配置）
Private Const API_URL As String = "https://api.niutrans.com/v2/text/translate"
Private Const APP_ID As String = "您的appId"      ' 请替换为您的实际appId
Private Const API_KEY As String = "您的apikey"   ' 请替换为您的实际apikey
Private Const FROM_LANG As String = "zh"
Private Const TO_LANG As String = "en"

' 测试API连接和配置
Sub TestNiuTransAPI()
    Dim testText As String
    Dim result As String
    
    ' 测试文本
    testText = "你好，世界！"
    
    MsgBox "开始测试小牛翻译API..." & vbCrLf & _
           "测试文本：" & testText, vbInformation, "API测试"
    
    ' 调用翻译API
    result = TranslateText(testText)
    
    If result <> "" Then
        MsgBox "API测试成功！" & vbCrLf & _
               "原文：" & testText & vbCrLf & _
               "译文：" & result, vbInformation, "测试成功"
    Else
        MsgBox "API测试失败！" & vbCrLf & _
               "请检查：" & vbCrLf & _
               "1. APP_ID 和 API_KEY 是否正确" & vbCrLf & _
               "2. 网络连接是否正常" & vbCrLf & _
               "3. API配额是否充足" & vbCrLf & _
               "详细错误信息请查看立即窗口（Ctrl+G）", vbCritical, "测试失败"
    End If
End Sub

' 调用小牛翻译API（简化版本）
Function TranslateText(sourceText As String) As String
    On Error GoTo ErrorHandler
    
    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    
    Debug.Print "=== API测试开始 ==="
    Debug.Print "测试文本: " & sourceText
    Debug.Print "APP_ID: " & APP_ID
    Debug.Print "API_KEY: " & Left(API_KEY, 8) & "..." ' 只显示前8位
    
    ' 创建HTTP对象
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 生成时间戳
    timestamp = DateDiff("s", "1/1/1970", Now)
    Debug.Print "时间戳: " & timestamp
    
    ' 生成认证字符串
    authStr = GenerateAuthStr(sourceText, timestamp)
    Debug.Print "认证字符串: " & authStr
    
    ' 构建POST数据
    postData = "from=" & FROM_LANG & _
               "&to=" & TO_LANG & _
               "&appId=" & APP_ID & _
               "&timestamp=" & timestamp & _
               "&srcText=" & UrlEncode(sourceText) & _
               "&authStr=" & authStr
    
    Debug.Print "POST数据: " & postData
    
    ' 发送请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData
    
    ' 获取响应
    response = http.responseText
    Debug.Print "HTTP状态码: " & http.Status
    Debug.Print "API响应: " & response
    
    ' 简单解析响应
    If InStr(response, """code"":200") > 0 Then
        ' 提取翻译结果
        Dim startPos As Integer
        Dim endPos As Integer
        startPos = InStr(response, """translation"":""") + 15
        endPos = InStr(startPos, response, """")
        
        If startPos > 15 And endPos > startPos Then
            TranslateText = Mid(response, startPos, endPos - startPos)
            Debug.Print "翻译成功: " & TranslateText
        Else
            Debug.Print "解析翻译结果失败"
            TranslateText = ""
        End If
    Else
        Debug.Print "API返回错误"
        TranslateText = ""
    End If
    
    Debug.Print "=== API测试结束 ==="
    Exit Function
    
ErrorHandler:
    Debug.Print "API调用异常: " & Err.Description
    TranslateText = ""
End Function

' 生成认证字符串
Function GenerateAuthStr(srcText As String, timestamp As Long) As String
    Dim params As String
    Dim md5Hash As String
    
    ' 按字母顺序排列参数
    params = "apikey=" & API_KEY & _
             "&appId=" & APP_ID & _
             "&from=" & FROM_LANG & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=" & TO_LANG
    
    Debug.Print "MD5输入字符串: " & params
    
    ' 生成MD5哈希
    md5Hash = MD5Hash(params)
    GenerateAuthStr = md5Hash
End Function

' MD5哈希函数
Function MD5Hash(inputString As String) As String
    Dim md5 As Object
    Dim bytes() As Byte
    Dim i As Integer
    Dim result As String
    
    Set md5 = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    bytes = md5.ComputeHash_2(StrConv(inputString, vbFromUnicode))
    
    For i = 0 To UBound(bytes)
        result = result & Right("0" & Hex(bytes(i)), 2)
    Next i
    
    MD5Hash = LCase(result)
End Function

' URL编码函数
Function UrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i
    
    UrlEncode = result
End Function

' 检查配置完整性
Sub CheckConfiguration()
    Dim issues As String
    
    If APP_ID = "您的appId" Then
        issues = issues & "- APP_ID 未配置" & vbCrLf
    End If
    
    If API_KEY = "您的apikey" Then
        issues = issues & "- API_KEY 未配置" & vbCrLf
    End If
    
    If Len(APP_ID) < 10 Then
        issues = issues & "- APP_ID 长度可能不正确" & vbCrLf
    End If
    
    If Len(API_KEY) < 20 Then
        issues = issues & "- API_KEY 长度可能不正确" & vbCrLf
    End If
    
    If issues = "" Then
        MsgBox "配置检查通过！可以运行API测试。", vbInformation, "配置检查"
    Else
        MsgBox "配置检查发现问题：" & vbCrLf & issues & vbCrLf & _
               "请先修改配置再进行测试。", vbExclamation, "配置问题"
    End If
End Sub
