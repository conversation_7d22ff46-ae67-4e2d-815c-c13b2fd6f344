Option Explicit

' 声明Windows API Sleep函数
Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)

' 小牛翻译API测试脚本
' 用于验证API配置是否正确

' API配置（请修改为您的实际配置）
Private Const API_URL As String = "https://api.niutrans.com/v2/text/translate"
Private Const APP_ID As String = "x9Z1745649548376"  ' 请替换为您的实际appId
Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"  ' 请替换为您的实际apikey
Private Const FROM_LANG As String = "zh"
Private Const TO_LANG As String = "en"

' 测试API连接和配置
Sub TestNiuTransAPI()
    Dim testText As String
    Dim result As String
    
    ' 测试文本
    testText = "你好，世界！"
    
    MsgBox "开始测试小牛翻译API..." & vbCrLf & _
           "测试文本：" & testText, vbInformation, "API测试"
    
    ' 调用翻译API
    result = TranslateText(testText)
    
    If result <> "" Then
        MsgBox "API测试成功！" & vbCrLf & _
               "原文：" & testText & vbCrLf & _
               "译文：" & result, vbInformation, "测试成功"
    Else
        MsgBox "API测试失败！" & vbCrLf & _
               "请检查：" & vbCrLf & _
               "1. APP_ID 和 API_KEY 是否正确" & vbCrLf & _
               "2. 网络连接是否正常" & vbCrLf & _
               "3. API配额是否充足" & vbCrLf & _
               "详细错误信息请查看立即窗口（Ctrl+G）", vbCritical, "测试失败"
    End If
End Sub

' 调用小牛翻译API（简化版本）
Function TranslateText(sourceText As String) As String
    On Error GoTo ErrorHandler
    
    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    
    Debug.Print "=== API测试开始 ==="
    Debug.Print "测试文本: " & sourceText
    Debug.Print "APP_ID: " & APP_ID
    Debug.Print "API_KEY: " & Left(API_KEY, 8) & "..." ' 只显示前8位
    
    ' 创建HTTP对象
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 生成时间戳
    timestamp = DateDiff("s", "1/1/1970", Now)
    Debug.Print "时间戳: " & timestamp
    
    ' 生成认证字符串
    authStr = GenerateAuthStr(sourceText, timestamp)
    Debug.Print "认证字符串: " & authStr
    
    ' 构建POST数据
    postData = "from=" & FROM_LANG & _
               "&to=" & TO_LANG & _
               "&appId=" & APP_ID & _
               "&timestamp=" & timestamp & _
               "&srcText=" & UrlEncode(sourceText) & _
               "&authStr=" & authStr
    
    Debug.Print "POST数据: " & postData
    
    ' 发送请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData
    
    ' 获取响应
    response = http.responseText
    Debug.Print "HTTP状态码: " & http.Status
    Debug.Print "API响应: " & response
    
    ' 简单解析响应
    If InStr(response, """code"":200") > 0 Then
        ' 提取翻译结果
        Dim startPos As Integer
        Dim endPos As Integer
        startPos = InStr(response, """translation"":""") + 15
        endPos = InStr(startPos, response, """")
        
        If startPos > 15 And endPos > startPos Then
            TranslateText = Mid(response, startPos, endPos - startPos)
            Debug.Print "翻译成功: " & TranslateText
        Else
            Debug.Print "解析翻译结果失败"
            TranslateText = ""
        End If
    Else
        Debug.Print "API返回错误"
        TranslateText = ""
    End If
    
    Debug.Print "=== API测试结束 ==="
    Exit Function
    
ErrorHandler:
    Debug.Print "API调用异常: " & Err.Description
    TranslateText = ""
End Function

' 生成认证字符串
Function GenerateAuthStr(srcText As String, timestamp As Long) As String
    Dim params As String
    Dim md5Hash As String
    
    ' 按字母顺序排列参数
    params = "apikey=" & API_KEY & _
             "&appId=" & APP_ID & _
             "&from=" & FROM_LANG & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=" & TO_LANG
    
    Debug.Print "MD5输入字符串: " & params
    
    ' 生成MD5哈希
    md5Hash = MD5Hash(params)
    GenerateAuthStr = md5Hash
End Function

' MD5哈希函数
Function MD5Hash(inputString As String) As String
    Dim md5 As Object
    Dim bytes() As Byte
    Dim i As Integer
    Dim result As String
    
    Set md5 = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    bytes = md5.ComputeHash_2(StrConv(inputString, vbFromUnicode))
    
    For i = 0 To UBound(bytes)
        result = result & Right("0" & Hex(bytes(i)), 2)
    Next i
    
    MD5Hash = LCase(result)
End Function

' URL编码函数
Function UrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i
    
    UrlEncode = result
End Function

' 检查配置完整性
Sub CheckConfiguration()
    Dim issues As String
    
    If APP_ID = "您的appId" Then
        issues = issues & "- APP_ID 未配置" & vbCrLf
    End If
    
    If API_KEY = "您的apikey" Then
        issues = issues & "- API_KEY 未配置" & vbCrLf
    End If
    
    If Len(APP_ID) < 10 Then
        issues = issues & "- APP_ID 长度可能不正确" & vbCrLf
    End If
    
    If Len(API_KEY) < 20 Then
        issues = issues & "- API_KEY 长度可能不正确" & vbCrLf
    End If
    
    If issues = "" Then
        MsgBox "配置检查通过！可以运行API测试。", vbInformation, "配置检查"
    Else
        MsgBox "配置检查发现问题：" & vbCrLf & issues & vbCrLf & _
               "请先修改配置再进行测试。", vbExclamation, "配置问题"
    End If
End Sub

' 综合诊断函数 - 帮助找出翻译失败的原因
Sub DiagnosePPTTranslation()
    Debug.Print "=== PPT翻译问题诊断开始 ==="

    ' 1. 检查API配置
    Debug.Print "1. 检查API配置..."
    If APP_ID = "您的appId" Or API_KEY = "您的apikey" Then
        Debug.Print "❌ API配置未完成！请先配置APP_ID和API_KEY"
        MsgBox "请先配置API密钥！", vbCritical
        Exit Sub
    Else
        Debug.Print "✅ API配置已完成"
    End If

    ' 2. 测试API连接
    Debug.Print "2. 测试API连接..."
    Dim testResult As String
    testResult = TranslateText("测试")
    If testResult <> "" Then
        Debug.Print "✅ API连接正常，测试翻译结果: " & testResult
    Else
        Debug.Print "❌ API连接失败！请检查网络和API密钥"
        MsgBox "API连接失败！请检查网络和API密钥", vbCritical
        Exit Sub
    End If

    ' 3. 测试文本识别函数
    Debug.Print "3. 测试文本识别函数..."
    Debug.Print "测试中文文本'你好': " & IIf(IsEnglishOnly("你好"), "❌识别为英文", "✅识别为中文")
    Debug.Print "测试英文文本'Hello': " & IIf(IsEnglishOnly("Hello"), "✅识别为英文", "❌识别为中文")
    Debug.Print "测试混合文本'Hello你好': " & IIf(IsEnglishOnly("Hello你好"), "❌识别为英文", "✅识别为中文")

    ' 4. 检查PPT文件
    Debug.Print "4. 检查PPT文件..."
    Dim pptPath As String
    pptPath = "D:\Test\PPT翻译_提示语\待翻译\待翻译PPT.pptm"

    If Dir(pptPath) = "" Then
        Debug.Print "❌ PPT文件不存在: " & pptPath
        MsgBox "PPT文件不存在！", vbCritical
        Exit Sub
    Else
        Debug.Print "✅ PPT文件存在: " & pptPath
    End If

    ' 5. 分析PPT内容
    Debug.Print "5. 分析PPT内容..."
    AnalyzePPTContent pptPath

    Debug.Print "=== 诊断完成 ==="
    MsgBox "诊断完成！请查看立即窗口的详细信息。", vbInformation
End Sub

' 分析PPT内容
Sub AnalyzePPTContent(filePath As String)
    On Error GoTo ErrorHandler

    Dim ppt As Object
    Dim slide As Object
    Dim shape As Object
    Dim slideCount As Integer
    Dim textShapeCount As Integer
    Dim chineseTextCount As Integer

    Set ppt = GetObject(filePath)
    slideCount = ppt.Slides.Count

    Debug.Print "PPT总幻灯片数: " & slideCount

    Dim i As Integer, j As Integer
    For i = 1 To slideCount
        Set slide = ppt.Slides(i)
        Debug.Print "幻灯片 " & i & " 包含 " & slide.Shapes.Count & " 个形状"

        For j = 1 To slide.Shapes.Count
            Set shape = slide.Shapes(j)

            If shape.HasTextFrame Then
                If shape.TextFrame.HasText Then
                    textShapeCount = textShapeCount + 1
                    Dim text As String
                    text = shape.TextFrame.TextRange.text

                    Debug.Print "  形状 " & j & " (类型:" & shape.Type & ") 文本: " & Left(text, 50) & IIf(Len(text) > 50, "...", "")

                    If Not IsEnglishOnly(text) And Trim(text) <> "" Then
                        chineseTextCount = chineseTextCount + 1
                        Debug.Print "    ✅ 识别为需要翻译的中文文本"
                    Else
                        Debug.Print "    ⏭️ 跳过（英文或空文本）"
                    End If
                End If
            End If
        Next j
    Next i

    Debug.Print "总结:"
    Debug.Print "- 包含文本的形状数量: " & textShapeCount
    Debug.Print "- 需要翻译的中文文本数量: " & chineseTextCount

    If chineseTextCount = 0 Then
        Debug.Print "❌ 没有找到需要翻译的中文文本！"
        MsgBox "没有找到需要翻译的中文文本！可能原因：" & vbCrLf & _
               "1. PPT中没有中文文本" & vbCrLf & _
               "2. 文本识别函数有问题" & vbCrLf & _
               "3. 文本在不支持的形状类型中", vbExclamation
    End If

    ppt.Close
    Exit Sub

ErrorHandler:
    Debug.Print "❌ 分析PPT时出错: " & Err.Description
End Sub

' 检查文本是否为纯英文（从主文件复制）
Function IsEnglishOnly(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim asciiValue As Integer

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        asciiValue = Asc(char)

        ' 如果包含中文字符（Unicode范围），则不是纯英文
        If asciiValue > 127 Then
            IsEnglishOnly = False
            Exit Function
        End If
    Next i

    IsEnglishOnly = True
End Function
