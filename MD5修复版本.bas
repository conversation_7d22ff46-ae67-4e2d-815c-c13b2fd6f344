Option Explicit

' 声明Windows API Sleep函数
Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)

' 小牛翻译API配置
Private Const API_URL As String = "https://api.niutrans.com/v2/text/translate"
Private Const APP_ID As String = "您的appId"  ' 请替换为您的实际appId
Private Const API_KEY As String = "您的apikey"  ' 请替换为您的实际apikey
Private Const FROM_LANG As String = "zh"
Private Const TO_LANG As String = "en"

' 测试MD5修复版本
Sub TestMD5Fix()
    Dim testText As String
    Dim result As String
    
    testText = "你好，世界！"
    
    Debug.Print "=== MD5修复版本测试 ==="
    Debug.Print "测试文本: " & testText
    
    ' 测试翻译
    result = TranslateTextFixed(testText)
    
    If result <> "" Then
        Debug.Print "✅ 翻译成功: " & result
        MsgBox "MD5修复版本测试成功！" & vbCrLf & _
               "原文: " & testText & vbCrLf & _
               "译文: " & result, vbInformation
    Else
        Debug.Print "❌ 翻译失败"
        MsgBox "翻译失败，请查看立即窗口的详细信息", vbCritical
    End If
End Sub

' 修复版本的翻译函数
Function TranslateTextFixed(sourceText As String) As String
    On Error GoTo ErrorHandler
    
    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    
    Debug.Print "开始翻译: " & sourceText
    
    ' 创建HTTP对象
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 生成时间戳
    timestamp = DateDiff("s", "1/1/1970", Now)
    Debug.Print "时间戳: " & timestamp
    
    ' 生成认证字符串（使用修复版本）
    authStr = GenerateAuthStrFixed(sourceText, timestamp)
    Debug.Print "认证字符串: " & authStr
    
    ' 构建POST数据
    postData = "from=" & FROM_LANG & _
               "&to=" & TO_LANG & _
               "&appId=" & APP_ID & _
               "&timestamp=" & timestamp & _
               "&srcText=" & UrlEncode(sourceText) & _
               "&authStr=" & authStr
    
    Debug.Print "POST数据: " & postData
    
    ' 发送请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData
    
    ' 获取响应
    response = http.responseText
    Debug.Print "HTTP状态: " & http.Status
    Debug.Print "API响应: " & response
    
    ' 解析响应
    If InStr(response, """code"":200") > 0 Then
        Dim startPos As Integer
        Dim endPos As Integer
        startPos = InStr(response, """translation"":""") + 15
        endPos = InStr(startPos, response, """")
        
        If startPos > 15 And endPos > startPos Then
            TranslateTextFixed = Mid(response, startPos, endPos - startPos)
            Debug.Print "翻译成功: " & TranslateTextFixed
        Else
            Debug.Print "解析翻译结果失败"
            TranslateTextFixed = ""
        End If
    Else
        Debug.Print "API返回错误"
        TranslateTextFixed = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "翻译错误: " & Err.Description
    TranslateTextFixed = ""
End Function

' 修复版本的认证字符串生成
Function GenerateAuthStrFixed(srcText As String, timestamp As Long) As String
    Dim params As String
    
    ' 按字母顺序排列参数
    params = "apikey=" & API_KEY & _
             "&appId=" & APP_ID & _
             "&from=" & FROM_LANG & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=" & TO_LANG
    
    Debug.Print "MD5输入: " & params
    
    ' 使用修复版本的MD5
    GenerateAuthStrFixed = MD5HashFixed(params)
End Function

' 修复版本的MD5哈希函数
Function MD5HashFixed(inputString As String) As String
    On Error GoTo UseBackup
    
    ' 尝试方法1: 使用.NET Framework的MD5
    Dim md5 As Object
    Dim bytes As Variant
    Dim i As Integer
    Dim result As String
    
    Set md5 = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    
    ' 将字符串转换为字节数组
    Dim inputBytes As Variant
    inputBytes = StrConv(inputString, vbFromUnicode)
    
    ' 计算MD5哈希
    bytes = md5.ComputeHash_2(inputBytes)
    
    ' 转换为十六进制字符串
    For i = 0 To UBound(bytes)
        result = result & Right("0" & Hex(bytes(i)), 2)
    Next i
    
    MD5HashFixed = LCase(result)
    Debug.Print "MD5结果: " & MD5HashFixed
    Exit Function
    
UseBackup:
    Debug.Print "MD5计算失败，使用备用方法: " & Err.Description
    
    ' 备用方法: 使用Windows Script Host的加密对象
    On Error GoTo UseSimple
    
    Dim objMD5 As Object
    Set objMD5 = CreateObject("System.Security.Cryptography.HashAlgorithm")
    ' 这个方法可能在某些系统上不可用
    
UseSimple:
    Debug.Print "使用简化哈希算法"
    ' 最后的备用方案：简化哈希
    MD5HashFixed = SimpleHashFixed(inputString)
End Function

' 简化哈希算法（备用方案）
Function SimpleHashFixed(inputString As String) As String
    Dim i As Integer
    Dim j As Integer
    Dim hashValue As Long
    Dim result As String
    Dim tempHash As Long
    
    ' 使用更复杂的哈希算法
    For i = 1 To Len(inputString)
        tempHash = Asc(Mid(inputString, i, 1))
        hashValue = hashValue Xor (tempHash * (i Mod 256))
        hashValue = hashValue + tempHash * 31
    Next i
    
    ' 生成32位十六进制字符串
    For i = 1 To 4
        tempHash = hashValue Xor (hashValue \ (2 ^ (8 * (i - 1))))
        result = result & Right("00000000" & Hex(tempHash And &HFF), 2)
        hashValue = hashValue \ 256
    Next i
    
    ' 补充到32位
    While Len(result) < 32
        result = result & Right("00000000" & Hex(Len(inputString) * 17), 2)
    Wend
    
    SimpleHashFixed = LCase(Left(result, 32))
    Debug.Print "简化哈希结果: " & SimpleHashFixed
End Function

' URL编码函数
Function UrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i
    
    UrlEncode = result
End Function
