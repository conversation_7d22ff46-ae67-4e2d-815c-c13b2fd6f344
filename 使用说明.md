# PPT小牛翻译器使用说明

## 功能概述

这个VBA代码可以自动将PowerPoint演示文稿中的中文文本翻译成英文，使用小牛翻译API服务。

## 主要功能

### 支持的PPT元素类型
- ✅ 普通文本框
- ✅ 自动形状中的文本
- ✅ 占位符文本
- ✅ 组合形状（递归处理所有子形状）
- ✅ 图表元素：
  - 图表标题
  - X轴和Y轴标题
  - 图例
  - 数据标签
  - 系列名称
- ✅ 表格（所有单元格）
- ✅ SmartArt图形（所有节点）

### 排版保持功能
- 🔧 保持原始形状的宽度和高度
- 🔧 如果翻译后文本超出原始高度，自动调整字体大小
- 🔧 字体大小最小不低于1磅
- 🔧 使用AutoSize功能优化文本适配

### 翻译API集成
- 🌐 使用小牛翻译API（niutrans）
- 🌐 从中文（zh）翻译到英文（en）
- 🌐 包含API密钥认证
- 🌐 添加200毫秒请求延时避免超出QPS限制
- 🌐 完整的错误处理和调试信息输出

## 使用步骤

### 1. 准备工作

1. **确保文件路径正确**：
   - 源文件路径：`D:\Test\PPT翻译_提示语\待翻译\待翻译PPT.pptm`
   - 输出文件路径：`D:\Test\PPT翻译_提示语\待翻译\translated.pptx`

2. **检查API密钥**：
   - 当前使用的API密钥：`5448154cdabb5f148a2832f95a97df24`
   - 如需更换，请修改代码中的 `API_KEY` 常量

### 2. 导入VBA代码

1. 打开PowerPoint
2. 按 `Alt + F11` 打开VBA编辑器
3. 在项目资源管理器中右键点击，选择"插入" > "模块"
4. 将 `PPT小牛翻译器.bas` 文件中的代码复制到新模块中
5. 保存项目

### 3. 运行翻译

1. 在VBA编辑器中，按 `F5` 或点击"运行"按钮
2. 选择 `TranslatePPT` 子程序运行
3. 程序将自动：
   - 打开指定的PPT文件
   - 遍历所有幻灯片和形状
   - 提取中文文本并调用API翻译
   - 保存翻译后的文件

### 4. 查看结果

- 翻译完成后，程序会显示成功消息
- 翻译后的文件保存为：`D:\test\translated.pptx`
- 可以在VBA编辑器的"立即窗口"中查看详细的调试信息

## 配置选项

### 修改文件路径

在 `TranslatePPT` 子程序中修改以下变量：

```vba
sourcePath = "D:\Test\PPT翻译_提示语\待翻译\待翻译PPT.pptm"  ' 源文件路径
targetPath = "D:\Test\PPT翻译_提示语\待翻译\translated.pptx"  ' 输出文件路径
```

### 修改API配置

在代码顶部修改以下常量：

```vba
Private Const API_KEY As String = "您的API密钥"
Private Const API_URL As String = "http://api.niutrans.com/NiuTransServer/translation"
Private Const REQUEST_DELAY As Integer = 200 ' 请求延时（毫秒）
```

### 调整翻译语言

在 `ProcessTextShape` 等函数中修改翻译调用：

```vba
translatedText = TranslateText(originalText, "zh", "en")  ' 从中文到英文
' 可以改为其他语言对，如：
' translatedText = TranslateText(originalText, "en", "zh")  ' 从英文到中文
```

## 错误处理

### 常见问题及解决方案

1. **文件不存在错误**：
   - 检查源文件路径是否正确
   - 确保文件确实存在于指定位置

2. **API调用失败**：
   - 检查网络连接
   - 验证API密钥是否有效
   - 检查API配额是否用完

3. **权限错误**：
   - 确保对目标文件夹有写入权限
   - 检查文件是否被其他程序占用

4. **内存不足**：
   - 对于大型PPT文件，可能需要增加请求延时
   - 考虑分批处理大量文本

### 调试信息

程序会在VBA的"立即窗口"中输出详细的调试信息，包括：
- 当前处理的幻灯片编号
- 正在翻译的文本内容
- API响应信息
- 错误信息和堆栈跟踪

## 性能优化

### 提高翻译速度
- 减少 `REQUEST_DELAY` 值（但要注意API限制）
- 跳过纯英文文本的翻译
- 对空文本进行预过滤

### 减少API调用次数
- 程序自动跳过空文本和纯英文文本
- 避免重复翻译相同内容

## 注意事项

1. **API限制**：小牛翻译API有QPS限制，程序已添加200毫秒延时
2. **文本格式**：翻译后可能需要手动调整某些格式
3. **特殊字符**：包含特殊符号的文本可能需要额外处理
4. **文件备份**：建议在翻译前备份原始文件
5. **网络依赖**：需要稳定的网络连接来调用翻译API

## 技术支持

如遇到问题，请检查：
1. VBA编辑器的"立即窗口"中的错误信息
2. 网络连接状态
3. API密钥的有效性
4. 文件路径和权限设置

## 版本信息

- 版本：1.0
- 支持的PowerPoint版本：2010及以上
- 翻译服务：小牛翻译API
- 最后更新：2025年1月
