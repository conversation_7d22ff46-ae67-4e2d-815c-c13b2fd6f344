Option Explicit

' 最简单的测试 - 所有函数都在同一个文件中
Sub SimpleTest()
    Debug.Print "=== 简单功能测试开始 ==="
    
    ' 测试1: 基本MD5哈希
    Debug.Print "1. 测试MD5哈希..."
    Dim testInput As String
    Dim hashOutput As String
    
    testInput = "hello"
    hashOutput = SimpleMD5(testInput)
    Debug.Print "输入: " & testInput
    Debug.Print "MD5: " & hashOutput
    
    If Len(hashOutput) = 32 Then
        Debug.Print "✅ MD5长度正确"
    Else
        Debug.Print "❌ MD5长度错误: " & Len(hashOutput)
    End If
    
    ' 测试2: URL编码
    Debug.Print "2. 测试URL编码..."
    Dim urlInput As String
    Dim urlOutput As String
    
    urlInput = "你好世界"
    urlOutput = SimpleUrlEncode(urlInput)
    Debug.Print "输入: " & urlInput
    Debug.Print "编码: " & urlOutput
    
    ' 测试3: 中文识别
    Debug.Print "3. 测试中文识别..."
    Debug.Print "测试'你好': " & IIf(SimpleIsEnglish("你好"), "英文", "中文")
    Debug.Print "测试'Hello': " & IIf(SimpleIsEnglish("Hello"), "英文", "中文")
    
    ' 测试4: 完整认证字符串
    Debug.Print "4. 测试认证字符串生成..."
    Dim authString As String
    authString = SimpleAuth("测试文本", "testapp", "testkey", **********)
    Debug.Print "认证字符串: " & authString
    
    Debug.Print "=== 测试完成 ==="
    MsgBox "简单测试完成！请查看立即窗口结果。", vbInformation
End Sub

' 简化的MD5函数
Function SimpleMD5(input As String) As String
    On Error GoTo UseSimple
    
    ' 尝试使用系统MD5
    Dim md5 As Object
    Dim bytes As Variant
    Dim i As Integer
    Dim result As String
    
    Set md5 = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    bytes = md5.ComputeHash_2(StrConv(input, vbFromUnicode))
    
    For i = 0 To UBound(bytes)
        result = result & Right("0" & Hex(bytes(i)), 2)
    Next i
    
    SimpleMD5 = LCase(result)
    Exit Function
    
UseSimple:
    ' 备用简单哈希
    Dim hashValue As Long
    Dim tempResult As String
    
    For i = 1 To Len(input)
        hashValue = hashValue + Asc(Mid(input, i, 1)) * i * 31
    Next i
    
    ' 生成32位十六进制
    tempResult = Hex(hashValue)
    While Len(tempResult) < 32
        tempResult = tempResult & Hex(Len(input) * 17)
    Wend
    
    SimpleMD5 = LCase(Left(tempResult, 32))
End Function

' 简化的URL编码
Function SimpleUrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 数字和字母
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i
    
    SimpleUrlEncode = result
End Function

' 简化的英文检测
Function SimpleIsEnglish(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    
    ' 检查常见中文字符
    If InStr(text, "你") > 0 Or InStr(text, "好") > 0 Or InStr(text, "的") > 0 Then
        SimpleIsEnglish = False
        Exit Function
    End If
    
    ' 检查Unicode范围
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        If AscW(char) > 127 Then
            SimpleIsEnglish = False
            Exit Function
        End If
    Next i
    
    SimpleIsEnglish = True
End Function

' 简化的认证字符串生成
Function SimpleAuth(srcText As String, appId As String, apiKey As String, timestamp As Long) As String
    Dim params As String
    
    ' 按字母顺序组合参数
    params = "apikey=" & apiKey & _
             "&appId=" & appId & _
             "&from=zh" & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=en"
    
    Debug.Print "参数字符串: " & params
    
    ' 计算MD5
    SimpleAuth = SimpleMD5(params)
End Function

' 测试HTTP连接
Sub TestHTTP()
    Debug.Print "=== HTTP连接测试 ==="
    
    On Error GoTo HTTPError
    
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 测试简单的HTTP请求
    http.Open "GET", "https://www.baidu.com", False
    http.send
    
    Debug.Print "HTTP状态码: " & http.Status
    
    If http.Status = 200 Then
        Debug.Print "✅ HTTP连接正常"
    Else
        Debug.Print "❌ HTTP连接异常"
    End If
    
    Exit Sub
    
HTTPError:
    Debug.Print "❌ HTTP测试失败: " & Err.Description
End Sub

' 完整的翻译API测试（需要配置真实的API密钥）
Sub TestTranslationAPI()
    Debug.Print "=== 翻译API测试 ==="
    
    ' 这里需要替换为真实的API密钥
    Dim appId As String
    Dim apiKey As String
    
    appId = "您的appId"      ' 替换为真实值
    apiKey = "您的apikey"   ' 替换为真实值
    
    If appId = "您的appId" Or apiKey = "您的apikey" Then
        Debug.Print "❌ 请先配置真实的API密钥"
        MsgBox "请先在代码中配置真实的API密钥！", vbExclamation
        Exit Sub
    End If
    
    On Error GoTo APIError
    
    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    
    Set http = CreateObject("MSXML2.XMLHTTP")
    timestamp = DateDiff("s", "1/1/1970", Now)
    
    ' 生成认证字符串
    authStr = SimpleAuth("你好", appId, apiKey, timestamp)
    
    ' 构建POST数据
    postData = "from=zh&to=en&appId=" & appId & "&timestamp=" & timestamp & _
               "&srcText=" & SimpleUrlEncode("你好") & "&authStr=" & authStr
    
    Debug.Print "POST数据: " & postData
    
    ' 发送请求
    http.Open "POST", "https://api.niutrans.com/v2/text/translate", False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData
    
    response = http.responseText
    Debug.Print "API响应: " & response
    
    If InStr(response, """code"":200") > 0 Then
        Debug.Print "✅ API调用成功"
    Else
        Debug.Print "❌ API调用失败"
    End If
    
    Exit Sub
    
APIError:
    Debug.Print "❌ API测试错误: " & Err.Description
End Sub
