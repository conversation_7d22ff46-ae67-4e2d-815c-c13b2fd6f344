Option Explicit

' Windows API声明用于延时
#If VBA7 Then
    Private Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#Else
    Private Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If

' 改进版小牛翻译API测试模块
' 使用POST方法和正确的UTF-8编码

Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"
Private Const API_URL As String = "http://api.niutrans.com/NiuTransServer/translation"

' 改进版API测试
Sub TestNiuTransAPIImproved()
    Debug.Print "=== 改进版小牛翻译API测试开始 ==="
    Debug.Print "时间: " & Now()
    Debug.Print "使用POST方法和UTF-8编码"
    Debug.Print ""
    
    ' 测试用例
    Dim testCases As Variant
    testCases = Array( _
        "你好，世界！", _
        "这是一个测试文本。", _
        "PowerPoint演示文稿", _
        "数据分析报告", _
        "项目管理计划", _
        "人工智能技术", _
        "机器学习算法" _
    )
    
    Dim i As Integer
    Dim originalText As String
    Dim translatedText As String
    Dim success As Integer
    Dim total As Integer
    
    total = UBound(testCases) + 1
    success = 0
    
    ' 逐个测试翻译
    For i = 0 To UBound(testCases)
        originalText = testCases(i)
        Debug.Print "测试 " & (i + 1) & "/" & total & ":"
        Debug.Print "原文: " & originalText
        
        translatedText = TranslateTextImproved(originalText, "zh", "en")
        
        If Len(translatedText) > 0 And translatedText <> originalText And Not IsGarbledText(translatedText) Then
            Debug.Print "译文: " & translatedText
            Debug.Print "状态: ✓ 成功"
            success = success + 1
        Else
            Debug.Print "译文: [翻译失败或乱码]"
            Debug.Print "状态: ✗ 失败"
        End If
        
        Debug.Print ""
        
        ' 添加延时避免API限制
        Sleep 300
    Next i
    
    ' 输出测试结果
    Debug.Print "=== 测试结果汇总 ==="
    Debug.Print "总测试数: " & total
    Debug.Print "成功数: " & success
    Debug.Print "失败数: " & (total - success)
    Debug.Print "成功率: " & Format(success / total * 100, "0.0") & "%"
    
    If success = total Then
        Debug.Print "状态: 🎉 所有测试通过！API工作正常。"
        MsgBox "API测试完成！所有测试通过，翻译功能正常。", vbInformation, "测试成功"
    ElseIf success > 0 Then
        Debug.Print "状态: ⚠️ 部分测试通过，请检查网络或API配置。"
        MsgBox "API测试完成！部分测试通过，请检查调试窗口了解详情。", vbExclamation, "部分成功"
    Else
        Debug.Print "状态: ❌ 所有测试失败，请检查API密钥和网络连接。"
        MsgBox "API测试失败！请检查API密钥和网络连接。", vbCritical, "测试失败"
    End If
    
    Debug.Print "=== 测试结束 ==="
End Sub

' 改进版翻译函数（使用POST方法）
Function TranslateTextImproved(sourceText As String, fromLang As String, toLang As String) As String
    On Error GoTo ErrorHandler
    
    Dim startTime As Double
    startTime = Timer
    
    ' 清理文本
    sourceText = Trim(sourceText)
    If Len(sourceText) = 0 Then
        TranslateTextImproved = ""
        Exit Function
    End If
    
    Debug.Print "  发送POST请求..."
    
    ' 创建HTTP请求对象
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' 构建POST数据
    Dim postData As String
    postData = "from=" & fromLang & "&to=" & toLang & "&apikey=" & API_KEY & "&src_text=" & sourceText
    
    Debug.Print "  请求URL: " & API_URL
    Debug.Print "  POST数据: " & Left(postData, 100) & "..."
    
    ' 发送POST请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded; charset=UTF-8"
    http.setRequestHeader "Accept", "application/json"
    http.setRequestHeader "User-Agent", "VBA-PowerPoint-Translator/1.0"
    
    ' 发送请求
    http.send postData
    
    Dim responseTime As Double
    responseTime = Timer - startTime
    
    Debug.Print "  响应时间: " & Format(responseTime, "0.000") & "秒"
    Debug.Print "  HTTP状态: " & http.Status
    
    ' 检查响应状态
    If http.Status = 200 Then
        Dim responseText As String
        responseText = http.responseText
        
        Debug.Print "  响应内容: " & Left(responseText, 200) & "..."
        
        ' 解析JSON响应
        Dim translatedText As String
        translatedText = ParseTranslationResponseImproved(responseText)
        
        If Len(translatedText) > 0 Then
            TranslateTextImproved = translatedText
        Else
            Debug.Print "  错误: 无法解析翻译结果"
            TranslateTextImproved = ""
        End If
    Else
        Debug.Print "  错误: HTTP请求失败，状态码 " & http.Status
        If Len(http.responseText) > 0 Then
            Debug.Print "  错误详情: " & http.responseText
        End If
        TranslateTextImproved = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "  异常: " & Err.Description & " (错误号: " & Err.Number & ")"
    TranslateTextImproved = ""
End Function

' 改进版JSON解析函数
Function ParseTranslationResponseImproved(jsonResponse As String) As String
    On Error GoTo ErrorHandler
    
    Debug.Print "  解析JSON响应..."
    
    ' 检查是否包含错误信息
    If InStr(jsonResponse, """error""") > 0 Or InStr(jsonResponse, """error_code""") > 0 Then
        Debug.Print "  API返回错误: " & jsonResponse
        ParseTranslationResponseImproved = ""
        Exit Function
    End If
    
    ' 查找"tgt_text":"
    Dim startPos As Integer
    Dim endPos As Integer
    Dim targetText As String
    
    startPos = InStr(jsonResponse, """tgt_text"":""")
    If startPos > 0 Then
        startPos = startPos + 12 ' 跳过"tgt_text":"
        endPos = InStr(startPos, jsonResponse, """")
        If endPos > startPos Then
            targetText = Mid(jsonResponse, startPos, endPos - startPos)
            
            ' 处理JSON转义字符
            targetText = Replace(targetText, "\n", vbCrLf)
            targetText = Replace(targetText, "\r", "")
            targetText = Replace(targetText, "\t", vbTab)
            targetText = Replace(targetText, "\\", "\")
            targetText = Replace(targetText, "\""", """")
            targetText = Replace(targetText, "\/", "/")
            
            ' 清理可能的乱码
            targetText = Trim(targetText)
            
            Debug.Print "  解析成功，提取到翻译文本: " & targetText
            ParseTranslationResponseImproved = targetText
        Else
            Debug.Print "  解析失败：找不到结束引号"
            ParseTranslationResponseImproved = ""
        End If
    Else
        Debug.Print "  解析失败：找不到tgt_text字段"
        Debug.Print "  完整响应: " & jsonResponse
        ParseTranslationResponseImproved = ""
    End If
    
    Exit Function
    
ErrorHandler:
    Debug.Print "  JSON解析异常: " & Err.Description
    ParseTranslationResponseImproved = ""
End Function

' 检查文本是否为乱码
Function IsGarbledText(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim questionMarkCount As Integer
    Dim totalChars As Integer
    
    totalChars = Len(text)
    If totalChars = 0 Then
        IsGarbledText = True
        Exit Function
    End If
    
    ' 统计问号数量
    For i = 1 To totalChars
        char = Mid(text, i, 1)
        If char = "?" Then
            questionMarkCount = questionMarkCount + 1
        End If
    Next i
    
    ' 如果问号超过50%，认为是乱码
    If questionMarkCount / totalChars > 0.5 Then
        IsGarbledText = True
    Else
        IsGarbledText = False
    End If
End Function

' 快速测试改进版翻译
Sub QuickTestImproved()
    Dim testText As String
    Dim result As String
    
    testText = InputBox("请输入要翻译的中文文本:", "快速测试（改进版）", "你好，世界！")
    
    If Len(testText) > 0 Then
        Debug.Print "=== 快速翻译测试（改进版） ==="
        Debug.Print "原文: " & testText
        
        result = TranslateTextImproved(testText, "zh", "en")
        
        If Len(result) > 0 And Not IsGarbledText(result) Then
            Debug.Print "译文: " & result
            MsgBox "翻译成功！" & vbCrLf & vbCrLf & "原文: " & testText & vbCrLf & "译文: " & result, vbInformation
        Else
            Debug.Print "翻译失败或结果为乱码"
            MsgBox "翻译失败，请检查网络连接和API配置。", vbCritical
        End If
    End If
End Sub

' 测试不同的编码方法
Sub TestEncodingMethods()
    Debug.Print "=== 编码方法测试 ==="
    
    Dim testText As String
    testText = "你好世界"
    
    Debug.Print "原始文本: " & testText
    Debug.Print "直接传递: " & testText
    Debug.Print "ASCII码: " & Asc(Mid(testText, 1, 1))
    
    ' 测试不同的API调用方法
    Debug.Print ""
    Debug.Print "测试1: 直接POST传递"
    Dim result1 As String
    result1 = TranslateTextImproved(testText, "zh", "en")
    Debug.Print "结果1: " & result1
    
    Debug.Print "=== 编码测试结束 ==="
End Sub


