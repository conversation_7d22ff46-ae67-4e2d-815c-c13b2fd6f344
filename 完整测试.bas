Option Explicit

' 完整的独立测试模块 - 包含所有必要的函数

' 主测试函数
Sub RunCompleteTest()
    Debug.Print "=== 完整功能测试开始 ==="
    Debug.Print "时间: " & Now
    
    ' 清空之前的输出
    Debug.Print String(50, "=")
    
    ' 测试1: MD5哈希功能
    TestMD5Function
    
    ' 测试2: URL编码功能
    TestUrlEncoding
    
    ' 测试3: 文本识别功能
    TestTextRecognition
    
    ' 测试4: 认证字符串生成
    TestAuthGeneration
    
    ' 测试5: HTTP连接
    TestHTTPConnection
    
    Debug.Print String(50, "=")
    Debug.Print "=== 所有测试完成 ==="
    
    MsgBox "测试完成！请查看立即窗口(Ctrl+G)的详细结果。", vbInformation, "测试完成"
End Sub

' 测试MD5哈希功能
Sub TestMD5Function()
    Debug.Print "1. 测试MD5哈希功能..."
    
    Dim testCases As Variant
    Dim i As Integer
    Dim result As String
    
    ' 测试用例
    testCases = Array("hello", "world", "test123", "你好")
    
    For i = 0 To UBound(testCases)
        result = CalculateMD5(CStr(testCases(i)))
        Debug.Print "  输入: " & testCases(i) & " -> MD5: " & result
        
        If Len(result) = 32 Then
            Debug.Print "  ✅ 长度正确"
        Else
            Debug.Print "  ❌ 长度错误: " & Len(result)
        End If
    Next i
End Sub

' 测试URL编码功能
Sub TestUrlEncoding()
    Debug.Print "2. 测试URL编码功能..."
    
    Dim testCases As Variant
    Dim i As Integer
    Dim result As String
    
    testCases = Array("hello world", "你好世界", "test@123", "a+b=c")
    
    For i = 0 To UBound(testCases)
        result = EncodeURL(CStr(testCases(i)))
        Debug.Print "  输入: " & testCases(i) & " -> 编码: " & result
    Next i
End Sub

' 测试文本识别功能
Sub TestTextRecognition()
    Debug.Print "3. 测试文本识别功能..."
    
    Dim testCases As Variant
    Dim i As Integer
    Dim isEng As Boolean
    
    testCases = Array("Hello World", "你好世界", "Hello你好", "123ABC", "测试文本")
    
    For i = 0 To UBound(testCases)
        isEng = IsTextEnglish(CStr(testCases(i)))
        Debug.Print "  文本: " & testCases(i) & " -> " & IIf(isEng, "英文", "中文")
    Next i
End Sub

' 测试认证字符串生成
Sub TestAuthGeneration()
    Debug.Print "4. 测试认证字符串生成..."
    
    Dim authStr As String
    authStr = GenerateAuthString("测试文本", "testapp", "testkey", 1640995200)
    
    Debug.Print "  认证字符串: " & authStr
    Debug.Print "  长度: " & Len(authStr)
End Sub

' 测试HTTP连接
Sub TestHTTPConnection()
    Debug.Print "5. 测试HTTP连接..."
    
    On Error GoTo HTTPError
    
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    Debug.Print "  ✅ HTTP对象创建成功"
    
    ' 测试简单请求
    http.Open "GET", "https://www.baidu.com", False
    http.send
    
    Debug.Print "  HTTP状态码: " & http.Status
    
    If http.Status = 200 Then
        Debug.Print "  ✅ HTTP连接正常"
    Else
        Debug.Print "  ❌ HTTP连接异常"
    End If
    
    Exit Sub
    
HTTPError:
    Debug.Print "  ❌ HTTP测试失败: " & Err.Description
End Sub

' MD5哈希计算函数
Function CalculateMD5(inputText As String) As String
    On Error GoTo UseBackupHash
    
    ' 尝试使用系统MD5
    Dim md5Object As Object
    Dim hashBytes As Variant
    Dim i As Integer
    Dim hexResult As String
    
    Set md5Object = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    hashBytes = md5Object.ComputeHash_2(StrConv(inputText, vbFromUnicode))
    
    For i = 0 To UBound(hashBytes)
        hexResult = hexResult & Right("0" & Hex(hashBytes(i)), 2)
    Next i
    
    CalculateMD5 = LCase(hexResult)
    Exit Function
    
UseBackupHash:
    ' 备用哈希算法
    Debug.Print "  使用备用哈希算法: " & Err.Description
    CalculateMD5 = BackupHash(inputText)
End Function

' 备用哈希算法
Function BackupHash(inputText As String) As String
    Dim hashValue As Long
    Dim i As Integer
    Dim char As String
    Dim result As String
    
    ' 简单哈希计算
    hashValue = 0
    For i = 1 To Len(inputText)
        char = Mid(inputText, i, 1)
        hashValue = hashValue + Asc(char) * (i + 31)
        hashValue = hashValue Mod **********  ' 防止溢出
    Next i
    
    ' 转换为十六进制并补足32位
    result = Hex(hashValue)
    
    ' 补足到32位
    While Len(result) < 32
        result = result & Hex(Len(inputText) * 17 + i)
        i = i + 1
    Wend
    
    BackupHash = LCase(Left(result, 32))
End Function

' URL编码函数
Function EncodeURL(inputText As String) As String
    Dim i As Integer
    Dim char As String
    Dim asciiCode As Integer
    Dim result As String
    
    For i = 1 To Len(inputText)
        char = Mid(inputText, i, 1)
        asciiCode = Asc(char)
        
        Select Case asciiCode
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(asciiCode), 2)
        End Select
    Next i
    
    EncodeURL = result
End Function

' 文本语言识别函数
Function IsTextEnglish(inputText As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim unicodeValue As Long
    
    ' 快速检查常见中文字符
    If InStr(inputText, "的") > 0 Or InStr(inputText, "是") > 0 Or _
       InStr(inputText, "在") > 0 Or InStr(inputText, "了") > 0 Or _
       InStr(inputText, "和") > 0 Or InStr(inputText, "有") > 0 Or _
       InStr(inputText, "你") > 0 Or InStr(inputText, "好") > 0 Then
        IsTextEnglish = False
        Exit Function
    End If
    
    ' 检查Unicode字符
    For i = 1 To Len(inputText)
        char = Mid(inputText, i, 1)
        unicodeValue = AscW(char)
        
        ' 中文字符范围
        If unicodeValue >= &H4E00 And unicodeValue <= &H9FFF Then
            IsTextEnglish = False
            Exit Function
        End If
        
        ' 其他非ASCII字符
        If unicodeValue > 255 Then
            IsTextEnglish = False
            Exit Function
        End If
    Next i
    
    IsTextEnglish = True
End Function

' 认证字符串生成函数
Function GenerateAuthString(srcText As String, appId As String, apiKey As String, timestamp As Long) As String
    Dim paramString As String
    
    ' 按字母顺序组合参数
    paramString = "apikey=" & apiKey & _
                  "&appId=" & appId & _
                  "&from=zh" & _
                  "&srcText=" & srcText & _
                  "&timestamp=" & CStr(timestamp) & _
                  "&to=en"
    
    Debug.Print "  参数字符串: " & paramString
    
    ' 计算MD5
    GenerateAuthString = CalculateMD5(paramString)
End Function

' 测试真实API调用（需要配置API密钥）
Sub TestRealAPI()
    Debug.Print "=== 真实API测试 ==="
    
    ' 从API测试.bas文件中读取的API密钥
    Dim appId As String
    Dim apiKey As String
    
    appId = "您的appId"  ' 需要替换
    apiKey = "5448154cdabb5f148a2832f95a97df24"  ' 您提供的API密钥
    
    If appId = "您的appId" Then
        Debug.Print "❌ 请配置APP_ID"
        MsgBox "请先配置APP_ID！", vbExclamation
        Exit Sub
    End If
    
    CallTranslationAPI "你好世界", appId, apiKey
End Sub

' 调用翻译API
Sub CallTranslationAPI(textToTranslate As String, appId As String, apiKey As String)
    On Error GoTo APIError
    
    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    
    Set http = CreateObject("MSXML2.XMLHTTP")
    timestamp = DateDiff("s", "1/1/1970", Now)
    
    ' 生成认证字符串
    authStr = GenerateAuthString(textToTranslate, appId, apiKey, timestamp)
    
    ' 构建POST数据
    postData = "from=zh&to=en" & _
               "&appId=" & appId & _
               "&timestamp=" & CStr(timestamp) & _
               "&srcText=" & EncodeURL(textToTranslate) & _
               "&authStr=" & authStr
    
    Debug.Print "POST数据: " & postData
    
    ' 发送请求
    http.Open "POST", "https://api.niutrans.com/v2/text/translate", False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData
    
    response = http.responseText
    Debug.Print "API响应: " & response
    
    ' 简单解析结果
    If InStr(response, """code"":200") > 0 Then
        Debug.Print "✅ API调用成功"
        
        ' 提取翻译结果
        Dim startPos As Integer
        Dim endPos As Integer
        startPos = InStr(response, """translation"":""") + 15
        endPos = InStr(startPos, response, """")
        
        If startPos > 15 And endPos > startPos Then
            Dim translation As String
            translation = Mid(response, startPos, endPos - startPos)
            Debug.Print "翻译结果: " & translation
            MsgBox "翻译成功！" & vbCrLf & "原文: " & textToTranslate & vbCrLf & "译文: " & translation, vbInformation
        End If
    Else
        Debug.Print "❌ API调用失败"
        MsgBox "API调用失败，请检查参数和网络连接", vbCritical
    End If
    
    Exit Sub
    
APIError:
    Debug.Print "❌ API调用错误: " & Err.Description
    MsgBox "API调用出错: " & Err.Description, vbCritical
End Sub
