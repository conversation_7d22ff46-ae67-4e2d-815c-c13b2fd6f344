Option Explicit

' 声明Windows API Sleep函数
Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)

' 小牛翻译API配置
Private Const API_URL As String = "https://api.niutrans.com/v2/text/translate"
Private Const APP_ID As String = "x9Z1745649548376"  ' 请替换为您的实际appId
Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"  ' 请替换为您的实际apikey
Private Const FROM_LANG As String = "zh"
Private Const TO_LANG As String = "en"
Private Const REQUEST_DELAY As Integer = 200  ' 毫秒

' 主函数：翻译PPT文件
Sub TranslatePPT()
    Dim sourcePath As String
    Dim targetPath As String
    Dim ppt As Presentation
    Dim slide As slide
    Dim i As Integer
    
    ' 设置文件路径
    sourcePath = "D:\Test\PPT翻译_提示语\待翻译PPT.pptm"
    targetPath = "D:\Test\PPT翻译_提示语\translated.pptx"
    
    ' 检查源文件是否存在
    If Dir(sourcePath) = "" Then
        MsgBox "源文件不存在: " & sourcePath
        Exit Sub
    End If
    
    ' 打开PPT文件
    Set ppt = Presentations.Open(sourcePath)
    
    Debug.Print "开始翻译PPT文件: " & sourcePath
    Debug.Print "总共 " & ppt.Slides.Count & " 张幻灯片"
    
    ' 遍历所有幻灯片
    For i = 1 To ppt.Slides.Count
        Set slide = ppt.Slides(i)
        Debug.Print "正在处理第 " & i & " 张幻灯片..."
        
        ' 处理当前幻灯片的所有形状
        ProcessSlideShapes slide
        
        ' 添加延时避免API限制
        Sleep 1000  ' 延时1秒
    Next i
    
    ' 保存翻译后的文件
    ppt.SaveAs targetPath, ppSaveAsOpenXMLPresentation
    Debug.Print "翻译完成，已保存为: " & targetPath
    
    ' 关闭文件
    ppt.Close
    
    MsgBox "翻译完成！文件已保存为: " & targetPath
End Sub

' 处理幻灯片中的所有形状
Sub ProcessSlideShapes(slide As slide)
    Dim shape As shape
    Dim i As Integer
    
    For i = 1 To slide.Shapes.Count
        Set shape = slide.Shapes(i)
        ProcessShape shape
    Next i
End Sub

' 处理单个形状
Sub ProcessShape(shape As shape)
    Select Case shape.Type
        Case msoTextBox, msoAutoShape, msoPlaceholder
            ' 处理文本框和自动形状
            ProcessTextShape shape
            
        Case msoGroup
            ' 处理组合形状
            ProcessGroupShape shape
            
        Case msoChart
            ' 处理图表
            ProcessChart shape
            
        Case msoTable
            ' 处理表格
            ProcessTable shape
            
        Case msoSmartArt
            ' 处理SmartArt
            ProcessSmartArt shape
            
        Case Else
            ' 其他类型的形状，尝试处理文本
            If shape.HasTextFrame Then
                If shape.TextFrame.HasText Then
                    ProcessTextShape shape
                End If
            End If
    End Select
End Sub

' 处理文本形状
Sub ProcessTextShape(shape As shape)
    Dim originalText As String
    Dim translatedText As String
    Dim originalHeight As Single
    Dim originalFontSize As Single
    
    If Not shape.HasTextFrame Then Exit Sub
    If Not shape.TextFrame.HasText Then Exit Sub
    
    originalText = shape.TextFrame.TextRange.Text
    
    ' 跳过空文本或纯英文文本
    If Trim(originalText) = "" Or IsEnglishOnly(originalText) Then Exit Sub
    
    Debug.Print "原文: " & originalText
    
    ' 调用翻译API
    translatedText = TranslateText(originalText)
    
    If translatedText <> "" Then
        ' 记录原始尺寸和字体大小
        originalHeight = shape.Height
        originalFontSize = shape.TextFrame.TextRange.Font.Size
        
        ' 设置翻译后的文本
        shape.TextFrame.TextRange.Text = translatedText
        Debug.Print "译文: " & translatedText
        
        ' 调整文本适配
        AdjustTextFit shape, originalHeight, originalFontSize
        
        ' 添加延时
        Sleep 200  ' 延时200毫秒
    End If
End Sub

' 处理组合形状
Sub ProcessGroupShape(groupShape As shape)
    Dim i As Integer
    Dim childShape As shape
    
    For i = 1 To groupShape.GroupItems.Count
        Set childShape = groupShape.GroupItems(i)
        ProcessShape childShape
    Next i
End Sub

' 处理图表
Sub ProcessChart(chartShape As shape)
    On Error Resume Next
    
    Dim chart As Object
    Set chart = chartShape.chart
    
    If chart Is Nothing Then Exit Sub
    
    ' 翻译图表标题
    If chart.HasTitle Then
        TranslateChartElement chart.ChartTitle
    End If
    
    ' 翻译图例
    If chart.HasLegend Then
        Dim i As Integer
        For i = 1 To chart.Legend.LegendEntries.Count
            TranslateChartElement chart.Legend.LegendEntries(i)
        Next i
    End If
    
    ' 翻译轴标题
    TranslateAxisTitles chart
    
    On Error GoTo 0
End Sub

' 翻译图表元素
Sub TranslateChartElement(element As Object)
    On Error Resume Next
    
    Dim originalText As String
    Dim translatedText As String
    
    originalText = element.Text
    If originalText <> "" And Not IsEnglishOnly(originalText) Then
        translatedText = TranslateText(originalText)
        If translatedText <> "" Then
            element.Text = translatedText
            Sleep 200  ' 延时200毫秒
        End If
    End If
    
    On Error GoTo 0
End Sub

' 翻译轴标题
Sub TranslateAxisTitles(chart As Object)
    On Error Resume Next
    
    ' X轴标题
    If chart.Axes(1).HasTitle Then
        TranslateChartElement chart.Axes(1).AxisTitle
    End If
    
    ' Y轴标题
    If chart.Axes(2).HasTitle Then
        TranslateChartElement chart.Axes(2).AxisTitle
    End If
    
    On Error GoTo 0
End Sub

' 处理表格
Sub ProcessTable(tableShape As shape)
    Dim table As table
    Dim row As Integer
    Dim col As Integer
    Dim cellText As String
    Dim translatedText As String
    
    Set table = tableShape.table
    
    For row = 1 To table.Rows.Count
        For col = 1 To table.Columns.Count
            cellText = table.Cell(row, col).shape.TextFrame.TextRange.Text
            
            If cellText <> "" And Not IsEnglishOnly(cellText) Then
                translatedText = TranslateText(cellText)
                If translatedText <> "" Then
                    table.Cell(row, col).shape.TextFrame.TextRange.Text = translatedText
                    Sleep 200  ' 延时200毫秒
                End If
            End If
        Next col
    Next row
End Sub

' 处理SmartArt
Sub ProcessSmartArt(smartArtShape As shape)
    On Error Resume Next
    
    Dim smartArt As Object
    Dim node As Object
    Dim i As Integer
    
    Set smartArt = smartArtShape.SmartArt
    
    For i = 1 To smartArt.AllNodes.Count
        Set node = smartArt.AllNodes(i)
        If node.TextFrame2.HasText Then
            Dim originalText As String
            Dim translatedText As String
            
            originalText = node.TextFrame2.TextRange.Text
            If originalText <> "" And Not IsEnglishOnly(originalText) Then
                translatedText = TranslateText(originalText)
                If translatedText <> "" Then
                    node.TextFrame2.TextRange.Text = translatedText
                    Sleep 200  ' 延时200毫秒
                End If
            End If
        End If
    Next i
    
    On Error GoTo 0
End Sub

' 调用小牛翻译API
Function TranslateText(sourceText As String) As String
    On Error GoTo ErrorHandler

    Dim http As Object
    Dim timestamp As Long
    Dim authStr As String
    Dim postData As String
    Dim response As String
    Dim jsonResponse As Object

    ' 创建HTTP对象
    Set http = CreateObject("MSXML2.XMLHTTP")

    ' 生成时间戳
    timestamp = DateDiff("s", "1/1/1970", Now)

    ' 生成认证字符串
    authStr = GenerateAuthStr(sourceText, timestamp)

    ' 构建POST数据
    postData = "from=" & FROM_LANG & _
               "&to=" & TO_LANG & _
               "&appId=" & APP_ID & _
               "&timestamp=" & timestamp & _
               "&srcText=" & UrlEncode(sourceText) & _
               "&authStr=" & authStr

    ' 发送请求
    http.Open "POST", API_URL, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send postData

    ' 获取响应
    response = http.responseText
    Debug.Print "API响应: " & response

    ' 解析JSON响应
    Set jsonResponse = ParseJSON(response)

    If jsonResponse("code") = 200 Then
        TranslateText = jsonResponse("data")("translation")
    Else
        Debug.Print "翻译错误: " & jsonResponse("msg")
        TranslateText = ""
    End If

    Exit Function

ErrorHandler:
    Debug.Print "翻译API调用错误: " & Err.Description
    TranslateText = ""
End Function

' 生成认证字符串
Function GenerateAuthStr(srcText As String, timestamp As Long) As String
    Dim params As String
    Dim md5Hash As String

    ' 按字母顺序排列参数
    params = "apikey=" & API_KEY & _
             "&appId=" & APP_ID & _
             "&from=" & FROM_LANG & _
             "&srcText=" & srcText & _
             "&timestamp=" & timestamp & _
             "&to=" & TO_LANG

    ' 生成MD5哈希
    md5Hash = MD5Hash(params)
    GenerateAuthStr = md5Hash
End Function

' MD5哈希函数
Function MD5Hash(inputString As String) As String
    Dim md5 As Object
    Dim bytes() As Byte
    Dim i As Integer
    Dim result As String

    Set md5 = CreateObject("System.Security.Cryptography.MD5CryptoServiceProvider")
    bytes = md5.ComputeHash_2(StrConv(inputString, vbFromUnicode))

    For i = 0 To UBound(bytes)
        result = result & Right("0" & Hex(bytes(i)), 2)
    Next i

    MD5Hash = LCase(result)
End Function

' URL编码函数
Function UrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case Asc(char)
            Case 48 To 57, 65 To 90, 97 To 122  ' 0-9, A-Z, a-z
                result = result & char
            Case 32  ' 空格
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i

    UrlEncode = result
End Function

' 简单的JSON解析函数
Function ParseJSON(jsonString As String) As Object
    ' 这是一个简化的JSON解析器，仅用于处理小牛翻译API的响应
    ' 在实际应用中，建议使用更完整的JSON解析库

    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")

    ' 提取code值
    Dim codePos As Integer
    Dim codeValue As String
    codePos = InStr(jsonString, """code"":")
    If codePos > 0 Then
        codeValue = ExtractJSONValue(jsonString, codePos + 7)
        dict("code") = CLng(codeValue)
    End If

    ' 提取msg值
    Dim msgPos As Integer
    Dim msgValue As String
    msgPos = InStr(jsonString, """msg"":")
    If msgPos > 0 Then
        msgValue = ExtractJSONStringValue(jsonString, msgPos + 6)
        dict("msg") = msgValue
    End If

    ' 提取data对象
    Dim dataPos As Integer
    dataPos = InStr(jsonString, """data"":")
    If dataPos > 0 Then
        Dim dataDict As Object
        Set dataDict = CreateObject("Scripting.Dictionary")

        ' 提取translation值
        Dim transPos As Integer
        Dim transValue As String
        transPos = InStr(dataPos, jsonString, """translation"":")
        If transPos > 0 Then
            transValue = ExtractJSONStringValue(jsonString, transPos + 14)
            dataDict("translation") = transValue
        End If

        dict("data") = dataDict
    End If

    Set ParseJSON = dict
End Function

' 提取JSON数值
Function ExtractJSONValue(jsonString As String, startPos As Integer) As String
    Dim i As Integer
    Dim char As String
    Dim result As String

    For i = startPos To Len(jsonString)
        char = Mid(jsonString, i, 1)
        If char >= "0" And char <= "9" Then
            result = result & char
        Else
            Exit For
        End If
    Next i

    ExtractJSONValue = result
End Function

' 提取JSON字符串值
Function ExtractJSONStringValue(jsonString As String, startPos As Integer) As String
    Dim i As Integer
    Dim char As String
    Dim result As String
    Dim inString As Boolean

    ' 找到第一个引号
    For i = startPos To Len(jsonString)
        If Mid(jsonString, i, 1) = """" Then
            inString = True
            Exit For
        End If
    Next i

    ' 提取字符串内容
    If inString Then
        For i = i + 1 To Len(jsonString)
            char = Mid(jsonString, i, 1)
            If char = """" Then
                Exit For
            Else
                result = result & char
            End If
        Next i
    End If

    ExtractJSONStringValue = result
End Function

' 检查文本是否为纯英文
Function IsEnglishOnly(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim asciiValue As Integer

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        asciiValue = Asc(char)

        ' 如果包含中文字符（Unicode范围），则不是纯英文
        If asciiValue > 127 Then
            IsEnglishOnly = False
            Exit Function
        End If
    Next i

    IsEnglishOnly = True
End Function

' 调整文本适配
Sub AdjustTextFit(shape As shape, originalHeight As Single, originalFontSize As Single)
    On Error Resume Next

    Dim textFrame As TextFrame
    Dim currentFontSize As Single
    Dim minFontSize As Single

    Set textFrame = shape.TextFrame
    minFontSize = 1  ' 最小字体大小1磅

    ' 启用自动调整大小
    textFrame.AutoSize = ppAutoSizeShapeToFitText

    ' 如果高度超出原始高度，逐步减小字体
    If shape.Height > originalHeight * 1.1 Then  ' 允许10%的高度增长
        currentFontSize = textFrame.TextRange.Font.Size

        Do While shape.Height > originalHeight * 1.1 And currentFontSize > minFontSize
            currentFontSize = currentFontSize - 0.5
            textFrame.TextRange.Font.Size = currentFontSize
            DoEvents  ' 允许界面更新
        Loop

        ' 如果字体太小，恢复自动调整
        If currentFontSize <= minFontSize Then
            textFrame.TextRange.Font.Size = minFontSize
            textFrame.AutoSize = ppAutoSizeShapeToFitText
        End If
    End If

    On Error GoTo 0
End Sub

' 备用延时函数（如果Sleep不可用）
Sub DelayMs(milliseconds As Long)
    On Error Resume Next

    ' 尝试使用Sleep函数
    Sleep milliseconds

    ' 如果Sleep不可用，使用循环延时
    If Err.Number <> 0 Then
        Err.Clear
        Dim startTime As Double
        startTime = Timer
        Do While Timer < startTime + (milliseconds / 1000)
            DoEvents
        Loop
    End If

    On Error GoTo 0
End Sub
