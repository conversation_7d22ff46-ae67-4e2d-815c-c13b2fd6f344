Option Explicit

' 小牛翻译API配置
Private Const API_KEY As String = "5448154cdabb5f148a2832f95a97df24"
Private Const API_URL As String = "http://api.niutrans.com/NiuTransServer/translation"
Private Const REQUEST_DELAY As Integer = 200 ' 毫秒

' 主函数：翻译PPT文件
Sub TranslatePPT()
    Dim sourcePath As String
    Dim targetPath As String
    Dim ppt As Presentation
    Dim slide As slide
    Dim i As Integer
    
    ' 设置文件路径
    sourcePath = "D:\test\待翻译PPT.pptm"
    targetPath = "D:\test\translated.pptx"
    
    ' 检查源文件是否存在
    If Dir(sourcePath) = "" Then
        MsgBox "源文件不存在: " & sourcePath, vbCritical
        Exit Sub
    End If
    
    ' 打开PPT文件
    Set ppt = Presentations.Open(sourcePath)
    Debug.Print "已打开文件: " & sourcePath
    
    ' 遍历所有幻灯片
    For i = 1 To ppt.Slides.Count
        Set slide = ppt.Slides(i)
        Debug.Print "正在处理第 " & i & " 张幻灯片，共 " & ppt.Slides.Count & " 张"
        
        ' 处理幻灯片中的所有形状
        ProcessSlideShapes slide
        
        ' 添加延时避免API限制
        Sleep REQUEST_DELAY
    Next i
    
    ' 保存为新文件
    ppt.SaveAs targetPath, ppSaveAsOpenXMLPresentation
    Debug.Print "翻译完成，已保存为: " & targetPath
    
    ' 关闭文件
    ppt.Close
    
    MsgBox "翻译完成！文件已保存为: " & targetPath, vbInformation
End Sub

' 处理幻灯片中的所有形状
Sub ProcessSlideShapes(slide As slide)
    Dim shape As shape
    Dim i As Integer
    
    For i = 1 To slide.Shapes.Count
        Set shape = slide.Shapes(i)
        ProcessShape shape
    Next i
End Sub

' 处理单个形状
Sub ProcessShape(shape As shape)
    On Error GoTo ErrorHandler
    
    Select Case shape.Type
        Case msoTextBox, msoAutoShape, msoPlaceholder
            ' 处理文本框和自动形状
            ProcessTextShape shape
            
        Case msoGroup
            ' 处理组合形状
            ProcessGroupShape shape
            
        Case msoChart
            ' 处理图表
            ProcessChart shape
            
        Case msoTable
            ' 处理表格
            ProcessTable shape
            
        Case msoSmartArt
            ' 处理SmartArt
            ProcessSmartArt shape
            
        Case Else
            ' 其他类型尝试处理文本
            If shape.HasTextFrame Then
                If shape.TextFrame.HasText Then
                    ProcessTextShape shape
                End If
            End If
    End Select
    
    Exit Sub
    
ErrorHandler:
    Debug.Print "处理形状时出错: " & Err.Description & " (形状类型: " & shape.Type & ")"
End Sub

' 处理文本形状
Sub ProcessTextShape(shape As shape)
    On Error GoTo ErrorHandler
    
    If Not shape.HasTextFrame Then Exit Sub
    If Not shape.TextFrame.HasText Then Exit Sub
    
    Dim textRange As TextRange
    Set textRange = shape.TextFrame.TextRange
    
    Dim originalText As String
    Dim translatedText As String
    Dim originalHeight As Single
    Dim originalFontSize As Single
    
    originalText = Trim(textRange.Text)
    
    ' 跳过空文本或纯英文文本
    If Len(originalText) = 0 Or IsEnglishOnly(originalText) Then Exit Sub
    
    ' 记录原始尺寸
    originalHeight = shape.Height
    originalFontSize = textRange.Font.Size
    
    Debug.Print "翻译文本: " & Left(originalText, 50) & "..."
    
    ' 调用翻译API
    translatedText = TranslateText(originalText, "zh", "en")
    
    If Len(translatedText) > 0 And translatedText <> originalText Then
        ' 设置翻译后的文本
        textRange.Text = translatedText
        
        ' 调整文本适配
        AdjustTextFit shape, originalHeight, originalFontSize
        
        Debug.Print "翻译完成: " & Left(translatedText, 50) & "..."
    End If
    
    Exit Sub
    
ErrorHandler:
    Debug.Print "处理文本形状时出错: " & Err.Description
End Sub

' 处理组合形状
Sub ProcessGroupShape(groupShape As shape)
    On Error GoTo ErrorHandler
    
    Dim i As Integer
    Dim childShape As shape
    
    For i = 1 To groupShape.GroupItems.Count
        Set childShape = groupShape.GroupItems(i)
        ProcessShape childShape
    Next i
    
    Exit Sub
    
ErrorHandler:
    Debug.Print "处理组合形状时出错: " & Err.Description
End Sub

' 处理图表
Sub ProcessChart(chartShape As shape)
    On Error GoTo ErrorHandler
    
    If Not chartShape.HasChart Then Exit Sub
    
    Dim chart As chart
    Set chart = chartShape.chart
    
    ' 处理图表标题
    If chart.HasTitle Then
        If Len(Trim(chart.ChartTitle.Text)) > 0 Then
            Dim titleText As String
            titleText = TranslateText(chart.ChartTitle.Text, "zh", "en")
            If Len(titleText) > 0 Then
                chart.ChartTitle.Text = titleText
            End If
        End If
    End If
    
    ' 处理轴标题
    ProcessChartAxes chart
    
    ' 处理图例
    If chart.HasLegend Then
        ProcessChartLegend chart
    End If
    
    ' 处理数据标签
    ProcessChartDataLabels chart
    
    Exit Sub
    
ErrorHandler:
    Debug.Print "处理图表时出错: " & Err.Description
End Sub

' 处理图表轴
Sub ProcessChartAxes(chart As chart)
    On Error Resume Next
    
    ' X轴标题
    If chart.Axes(xlCategory).HasTitle Then
        Dim xTitle As String
        xTitle = TranslateText(chart.Axes(xlCategory).AxisTitle.Text, "zh", "en")
        If Len(xTitle) > 0 Then
            chart.Axes(xlCategory).AxisTitle.Text = xTitle
        End If
    End If
    
    ' Y轴标题
    If chart.Axes(xlValue).HasTitle Then
        Dim yTitle As String
        yTitle = TranslateText(chart.Axes(xlValue).AxisTitle.Text, "zh", "en")
        If Len(yTitle) > 0 Then
            chart.Axes(xlValue).AxisTitle.Text = yTitle
        End If
    End If
End Sub

' 处理图表图例
Sub ProcessChartLegend(chart As chart)
    On Error Resume Next
    
    Dim i As Integer
    Dim legendEntry As LegendEntry
    
    For i = 1 To chart.Legend.LegendEntries.Count
        Set legendEntry = chart.Legend.LegendEntries(i)
        Dim legendText As String
        legendText = TranslateText(legendEntry.LegendKey.Parent.Name, "zh", "en")
        If Len(legendText) > 0 Then
            ' 注意：图例文本通常与系列名称关联
            chart.SeriesCollection(i).Name = legendText
        End If
    Next i
End Sub

' 处理图表数据标签
Sub ProcessChartDataLabels(chart As chart)
    On Error Resume Next
    
    Dim series As series
    Dim i As Integer, j As Integer
    
    For i = 1 To chart.SeriesCollection.Count
        Set series = chart.SeriesCollection(i)
        
        If series.HasDataLabels Then
            For j = 1 To series.DataLabels.Count
                Dim labelText As String
                labelText = series.DataLabels(j).Text
                If Len(Trim(labelText)) > 0 And Not IsNumeric(labelText) Then
                    Dim translatedLabel As String
                    translatedLabel = TranslateText(labelText, "zh", "en")
                    If Len(translatedLabel) > 0 Then
                        series.DataLabels(j).Text = translatedLabel
                    End If
                End If
            Next j
        End If
    Next i
End Sub

' 处理表格
Sub ProcessTable(tableShape As shape)
    On Error GoTo ErrorHandler
    
    If Not tableShape.HasTable Then Exit Sub
    
    Dim table As table
    Set table = tableShape.table
    
    Dim row As Integer, col As Integer
    Dim cellText As String
    Dim translatedText As String
    
    For row = 1 To table.Rows.Count
        For col = 1 To table.Columns.Count
            cellText = Trim(table.Cell(row, col).shape.TextFrame.TextRange.Text)
            
            If Len(cellText) > 0 And Not IsEnglishOnly(cellText) Then
                translatedText = TranslateText(cellText, "zh", "en")
                If Len(translatedText) > 0 Then
                    table.Cell(row, col).shape.TextFrame.TextRange.Text = translatedText
                End If
            End If
        Next col
    Next row
    
    Exit Sub
    
ErrorHandler:
    Debug.Print "处理表格时出错: " & Err.Description
End Sub

' 处理SmartArt
Sub ProcessSmartArt(smartArtShape As shape)
    On Error GoTo ErrorHandler
    
    If Not smartArtShape.HasSmartArt Then Exit Sub
    
    Dim smartArt As SmartArt
    Set smartArt = smartArtShape.SmartArt
    
    Dim i As Integer
    Dim node As SmartArtNode
    
    For i = 1 To smartArt.AllNodes.Count
        Set node = smartArt.AllNodes(i)
        
        If Len(Trim(node.TextFrame2.TextRange.Text)) > 0 Then
            Dim nodeText As String
            Dim translatedNodeText As String
            
            nodeText = Trim(node.TextFrame2.TextRange.Text)
            
            If Not IsEnglishOnly(nodeText) Then
                translatedNodeText = TranslateText(nodeText, "zh", "en")
                If Len(translatedNodeText) > 0 Then
                    node.TextFrame2.TextRange.Text = translatedNodeText
                End If
            End If
        End If
    Next i
    
    Exit Sub

ErrorHandler:
    Debug.Print "处理SmartArt时出错: " & Err.Description
End Sub

' 调用小牛翻译API
Function TranslateText(sourceText As String, fromLang As String, toLang As String) As String
    On Error GoTo ErrorHandler

    ' 清理文本
    sourceText = Trim(sourceText)
    If Len(sourceText) = 0 Then
        TranslateText = ""
        Exit Function
    End If

    ' 创建HTTP请求对象
    Dim http As Object
    Set http = CreateObject("MSXML2.XMLHTTP")

    ' 构建请求URL和参数
    Dim url As String
    Dim params As String

    url = API_URL
    params = "from=" & fromLang & "&to=" & toLang & "&apikey=" & API_KEY & "&src_text=" & UrlEncode(sourceText)

    ' 发送GET请求
    http.Open "GET", url & "?" & params, False
    http.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
    http.send

    ' 检查响应状态
    If http.Status = 200 Then
        Dim responseText As String
        responseText = http.responseText

        Debug.Print "API响应: " & responseText

        ' 解析JSON响应
        Dim translatedText As String
        translatedText = ParseTranslationResponse(responseText)

        If Len(translatedText) > 0 Then
            TranslateText = translatedText
        Else
            TranslateText = sourceText ' 翻译失败时返回原文
        End If
    Else
        Debug.Print "API请求失败，状态码: " & http.Status
        TranslateText = sourceText
    End If

    Exit Function

ErrorHandler:
    Debug.Print "翻译API调用出错: " & Err.Description
    TranslateText = sourceText
End Function

' 解析翻译API响应
Function ParseTranslationResponse(jsonResponse As String) As String
    On Error GoTo ErrorHandler

    ' 简单的JSON解析，查找tgt_text字段
    Dim startPos As Integer
    Dim endPos As Integer
    Dim targetText As String

    ' 查找"tgt_text":"
    startPos = InStr(jsonResponse, """tgt_text"":""")
    If startPos > 0 Then
        startPos = startPos + 12 ' 跳过"tgt_text":"
        endPos = InStr(startPos, jsonResponse, """")
        If endPos > startPos Then
            targetText = Mid(jsonResponse, startPos, endPos - startPos)
            ' 处理JSON转义字符
            targetText = Replace(targetText, "\n", vbCrLf)
            targetText = Replace(targetText, "\r", "")
            targetText = Replace(targetText, "\t", vbTab)
            targetText = Replace(targetText, "\\", "\")
            targetText = Replace(targetText, "\""", """")
            ParseTranslationResponse = targetText
        End If
    End If

    Exit Function

ErrorHandler:
    Debug.Print "解析JSON响应时出错: " & Err.Description
    ParseTranslationResponse = ""
End Function

' URL编码函数
Function UrlEncode(text As String) As String
    Dim i As Integer
    Dim char As String
    Dim result As String

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        Select Case char
            Case "A" To "Z", "a" To "z", "0" To "9", "-", "_", ".", "~"
                result = result & char
            Case " "
                result = result & "%20"
            Case Else
                result = result & "%" & Right("0" & Hex(Asc(char)), 2)
        End Select
    Next i

    UrlEncode = result
End Function

' 检查文本是否只包含英文
Function IsEnglishOnly(text As String) As Boolean
    Dim i As Integer
    Dim char As String
    Dim asciiValue As Integer

    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        asciiValue = Asc(char)

        ' 如果包含中文字符（Unicode范围大致在19968-40959）
        If asciiValue > 255 Then
            IsEnglishOnly = False
            Exit Function
        End If
    Next i

    IsEnglishOnly = True
End Function

' 调整文本适配
Sub AdjustTextFit(shape As shape, originalHeight As Single, originalFontSize As Single)
    On Error GoTo ErrorHandler

    If Not shape.HasTextFrame Then Exit Sub

    Dim textFrame As TextFrame
    Set textFrame = shape.TextFrame

    ' 启用自动调整大小
    textFrame.AutoSize = ppAutoSizeShapeToFitText

    ' 如果高度超出原始高度，调整字体大小
    If shape.Height > originalHeight * 1.1 Then ' 允许10%的高度增长
        Dim textRange As TextRange
        Set textRange = textFrame.TextRange

        Dim newFontSize As Single
        newFontSize = originalFontSize

        ' 逐步减小字体大小直到适合
        Do While shape.Height > originalHeight * 1.1 And newFontSize > 1
            newFontSize = newFontSize - 0.5
            textRange.Font.Size = newFontSize
        Loop

        ' 确保字体大小不小于1磅
        If newFontSize < 1 Then
            textRange.Font.Size = 1
        End If

        Debug.Print "字体大小已调整: " & originalFontSize & " -> " & textRange.Font.Size
    End If

    ' 恢复原始高度约束
    textFrame.AutoSize = ppAutoSizeNone

    Exit Sub

ErrorHandler:
    Debug.Print "调整文本适配时出错: " & Err.Description
End Sub

' Windows API声明用于延时
#If VBA7 Then
    Private Declare PtrSafe Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#Else
    Private Declare Sub Sleep Lib "kernel32" (ByVal dwMilliseconds As Long)
#End If
