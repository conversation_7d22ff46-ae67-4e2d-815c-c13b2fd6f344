PPT小牛翻译器 - 配置说明
================================

1. API配置
----------
API密钥: 5448154cdabb5f148a2832f95a97df24
API地址: http://api.niutrans.com/NiuTransServer/translation
请求延时: 200毫秒（避免超出QPS限制）

如需更换API密钥，请修改以下文件中的API_KEY常量：
- PPT小牛翻译器.bas
- API测试.bas

2. 文件路径配置
--------------
默认源文件路径: D:\test\待翻译PPT.pptm
默认输出路径: D:\test\translated.pptx

修改方法：
在TranslatePPT()函数中修改以下变量：
sourcePath = "您的源文件路径"
targetPath = "您的输出文件路径"

3. 翻译语言配置
--------------
默认设置: 中文(zh) -> 英文(en)

支持的语言代码：
- zh: 中文
- en: 英文
- ja: 日语
- ko: 韩语
- fr: 法语
- de: 德语
- es: 西班牙语
- ru: 俄语

修改方法：
在各个处理函数中修改TranslateText调用：
TranslateText(text, "源语言代码", "目标语言代码")

4. 性能配置
----------
请求延时: REQUEST_DELAY = 200 (毫秒)
- 增加此值可减少API限制错误
- 减少此值可提高翻译速度（但可能触发限制）

字体调整阈值: originalHeight * 1.1
- 允许文本高度增长10%
- 超出此阈值将自动缩小字体

最小字体大小: 1磅
- 防止字体过小导致不可读

5. 调试配置
----------
调试信息输出: 默认启用
查看方法: VBA编辑器 -> 视图 -> 立即窗口

调试信息包括：
- 当前处理的幻灯片编号
- 正在翻译的文本内容
- API请求和响应信息
- 错误信息和异常详情
- 字体调整记录

6. 错误处理配置
--------------
API调用失败: 返回原文本（不中断处理）
网络超时: 自动跳过当前文本
JSON解析失败: 记录错误并继续处理
文件访问错误: 显示错误消息并停止

7. 支持的PPT元素
---------------
✓ 文本框 (msoTextBox)
✓ 自动形状 (msoAutoShape)  
✓ 占位符 (msoPlaceholder)
✓ 组合形状 (msoGroup) - 递归处理
✓ 图表 (msoChart) - 标题、轴、图例、数据标签
✓ 表格 (msoTable) - 所有单元格
✓ SmartArt (msoSmartArt) - 所有节点

8. 文本过滤规则
--------------
跳过条件：
- 空文本或仅包含空格
- 纯英文文本（ASCII字符）
- 纯数字文本
- 长度为0的文本

处理条件：
- 包含中文字符的文本
- 混合中英文文本
- 包含标点符号的中文文本

9. 安全设置
----------
文件备份: 建议在翻译前手动备份原文件
权限检查: 确保对输出目录有写入权限
网络安全: API使用HTTP协议，注意数据传输安全

10. 故障排除
-----------
常见问题及解决方案：

问题1: "文件不存在"错误
解决: 检查sourcePath路径是否正确

问题2: "API调用失败"错误  
解决: 检查网络连接和API密钥

问题3: "权限被拒绝"错误
解决: 检查输出目录写入权限

问题4: 翻译结果为空
解决: 运行API测试.bas中的TestNiuTransAPI()

问题5: 程序运行缓慢
解决: 减少REQUEST_DELAY值或检查网络速度

问题6: 字体显示异常
解决: 检查目标系统是否支持相应字体

11. 使用建议
-----------
1. 首次使用前运行API测试确认连接正常
2. 对重要文件先进行备份
3. 大型PPT文件建议分批处理
4. 翻译完成后检查格式和排版
5. 根据需要手动调整专业术语翻译

12. 版本兼容性
-------------
PowerPoint版本: 2010及以上
VBA版本: 6.0及以上（支持VBA7）
操作系统: Windows 7及以上
.NET Framework: 不需要

13. 许可和限制
-------------
API使用限制: 根据小牛翻译服务条款
QPS限制: 建议不超过5次/秒
文本长度限制: 单次请求建议不超过5000字符
日调用量限制: 根据API套餐而定
